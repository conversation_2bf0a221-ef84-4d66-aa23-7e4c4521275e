/**
 * TypeScript types for SVG parsing and processing
 */

export interface SVGPathCommand {
  type: 'M' | 'L' | 'C' | 'Q' | 'A' | 'Z' | 'H' | 'V' | 'S' | 'T';
  points: number[];
  absolute: boolean;
}

export interface SVGPoint {
  x: number;
  y: number;
}

export interface SVGBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface SVGTransform {
  type: 'translate' | 'rotate' | 'scale' | 'skewX' | 'skewY' | 'matrix';
  values: number[];
}

export interface SVGStyle {
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  strokeLinecap?: 'butt' | 'round' | 'square';
  strokeLinejoin?: 'miter' | 'round' | 'bevel';
  strokeDasharray?: string;
  strokeDashoffset?: number;
  opacity?: number;
  fillOpacity?: number;
  strokeOpacity?: number;
  fillRule?: 'nonzero' | 'evenodd';
}

export interface ParsedSVGElement {
  id?: string;
  type: 'path' | 'circle' | 'rect' | 'ellipse' | 'polygon' | 'polyline' | 'line' | 'g';
  attributes: Record<string, string>;
  style: SVGStyle;
  transform?: SVGTransform[];
  children?: ParsedSVGElement[];
  pathData?: string;
  bounds?: SVGBounds;
  // CAD/CAM specific properties
  layerName?: string;
  face?: 'top' | 'bottom';
  operationType?: 'groove' | 'pocket' | 'drill' | 'v-carve' | 'chamfer' | 'fillet';
  toolInfo?: {
    diameter?: number;
    type?: 'endmill' | 'v-bit' | 'ballnose';
    radius?: number;
  };
  machiningParams?: {
    depth?: number;
    startZ?: number;
    feedRate?: number;
    spindleSpeed?: number;
  };
}

export interface SVGParseOptions {
  preserveAspectRatio?: boolean;
  ignoreInvisible?: boolean;
  flattenGroups?: boolean;
  simplifyPaths?: boolean;
  tolerance?: number;
}

export interface SVGImportOptions {
  depth?: number;
  strokeWidth?: number;
  preserveAspectRatio?: boolean;
  scale?: number;
  centerOrigin?: boolean;
  extrudeMode?: 'uniform' | 'tapered' | 'none';
  materialProperties?: {
    color?: string;
    stroke?: string;
    fill?: boolean;
  };
  // CAD/CAM specific options
  face?: 'top' | 'bottom' | 'both';
  panelThickness?: number;
  operationType?: 'groove' | 'pocket' | 'drill' | 'v-carve' | 'chamfer' | 'fillet';
  toolRadius?: number;
  startingZ?: number;
  layerFiltering?: {
    showLMM?: boolean;
    showLabels?: boolean;
    showParameters?: boolean;
    visibleLayers?: string[];
  };
}

export interface SVGParseResult {
  elements: ParsedSVGElement[];
  viewBox: SVGBounds;
  width: number;
  height: number;
  errors: SVGParseError[];
}

export class SVGParseError extends Error {
  constructor(
    message: string,
    public readonly line?: number,
    public readonly column?: number,
    public readonly elementType?: string
  ) {
    super(message);
    this.name = 'SVGParseError';
  }
}

export class SVGRenderError extends Error {
  constructor(
    message: string,
    public readonly elementId?: string,
    public readonly elementType?: string
  ) {
    super(message);
    this.name = 'SVGRenderError';
  }
}

// Path command parsing types
export interface PathCommandParser {
  parseCommand(command: string, args: number[]): SVGPathCommand;
  validateCommand(command: SVGPathCommand): boolean;
}

export interface PathSegment {
  start: SVGPoint;
  end: SVGPoint;
  controlPoints?: SVGPoint[];
  type: 'line' | 'curve' | 'arc';
}

export interface CubicBezierSegment extends PathSegment {
  type: 'curve';
  controlPoints: [SVGPoint, SVGPoint];
}

export interface QuadraticBezierSegment extends PathSegment {
  type: 'curve';
  controlPoints: [SVGPoint];
}

export interface ArcSegment extends PathSegment {
  type: 'arc';
  rx: number;
  ry: number;
  xAxisRotation: number;
  largeArcFlag: boolean;
  sweepFlag: boolean;
}

export type ParsedPathSegment = PathSegment | CubicBezierSegment | QuadraticBezierSegment | ArcSegment;

// Color parsing types
export interface ColorRGB {
  r: number;
  g: number;
  b: number;
  a?: number;
}

export interface ColorHSL {
  h: number;
  s: number;
  l: number;
  a?: number;
}

export type ParsedColor = ColorRGB | ColorHSL | string;

// Utility types for SVG processing
export interface SVGProcessor {
  parseElement(element: Element): ParsedSVGElement;
  extractStyle(element: Element): SVGStyle;
  parseTransform(transformString: string): SVGTransform[];
  calculateBounds(element: ParsedSVGElement): SVGBounds;
}

export interface SVGOptimizer {
  simplifyPath(pathData: string, tolerance: number): string;
  mergeElements(elements: ParsedSVGElement[]): ParsedSVGElement[];
  removeInvisibleElements(elements: ParsedSVGElement[]): ParsedSVGElement[];
}
