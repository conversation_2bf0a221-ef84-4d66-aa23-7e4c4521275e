/**
 * CAD/CAM Parser for Door Manufacturing
 * Specialized parser for handling top/bottom face operations and layer management
 */

import { SVGParser } from './SVGParser';
import type { 
  ParsedSVGElement, 
  SVGImportOptions, 
  SVGParseResult 
} from './types/svg-types';

export interface CADCAMLayer {
  name: string;
  face: 'top' | 'bottom';
  operationType: 'groove' | 'pocket' | 'drill' | 'v-carve' | 'chamfer' | 'fillet' | 'panel';
  toolInfo?: {
    diameter: number;
    type: 'endmill' | 'v-bit' | 'ballnose';
    radius: number;
  };
  visible: boolean;
  elements: ParsedSVGElement[];
  zOffset: number; // Offset from panel surface
}

export interface DoorPanel {
  width: number;
  height: number;
  thickness: number;
  topLayers: CADCAMLayer[];
  bottomLayers: CADCAMLayer[];
  panelLayer: CADCAMLayer;
}

export class CADCAMParser extends SVGParser {
  private layerDetector: LayerToolDetector;
  private panelThickness: number = 18; // Default door panel thickness in mm

  constructor() {
    super();
    this.layerDetector = new LayerToolDetector();
  }

  /**
   * Parse SVG with CAD/CAM layer awareness
   */
  public parseCADCAMSVG(svgContent: string, options: SVGImportOptions = {}): DoorPanel {
    // Parse the SVG normally first
    const parseResult = this.parseSVG(svgContent);
    
    // Set panel thickness from options
    this.panelThickness = options.panelThickness || 18;
    
    // Extract layers from parsed elements
    const layers = this.extractLayers(parseResult.elements, options);
    
    // Separate into top and bottom face operations
    const topLayers = layers.filter(layer => layer.face === 'top');
    const bottomLayers = layers.filter(layer => layer.face === 'bottom');
    const panelLayer = layers.find(layer => layer.operationType === 'panel') || this.createDefaultPanelLayer();
    
    return {
      width: parseResult.width,
      height: parseResult.height,
      thickness: this.panelThickness,
      topLayers: topLayers.filter(l => l.operationType !== 'panel'),
      bottomLayers: bottomLayers.filter(l => l.operationType !== 'panel'),
      panelLayer
    };
  }

  /**
   * Extract layers from SVG elements with CAD/CAM context
   */
  private extractLayers(elements: ParsedSVGElement[], options: SVGImportOptions): CADCAMLayer[] {
    const layerMap = new Map<string, ParsedSVGElement[]>();
    
    // Group elements by layer
    this.groupElementsByLayer(elements, layerMap);
    
    const layers: CADCAMLayer[] = [];
    
    for (const [layerName, layerElements] of layerMap) {
      // Skip filtered layers
      if (this.shouldSkipLayer(layerName, options)) {
        continue;
      }
      
      const layer = this.createCADCAMLayer(layerName, layerElements, options);
      if (layer) {
        layers.push(layer);
      }
    }
    
    return layers;
  }

  /**
   * Group elements by layer name
   */
  private groupElementsByLayer(elements: ParsedSVGElement[], layerMap: Map<string, ParsedSVGElement[]>): void {
    for (const element of elements) {
      const layerName = this.extractLayerName(element);
      
      if (!layerMap.has(layerName)) {
        layerMap.set(layerName, []);
      }
      
      // Add layer information to element
      element.layerName = layerName;
      layerMap.get(layerName)!.push(element);
      
      // Recursively process children
      if (element.children) {
        this.groupElementsByLayer(element.children, layerMap);
      }
    }
  }

  /**
   * Extract layer name from SVG element
   */
  private extractLayerName(element: ParsedSVGElement): string {
    // Check various attributes for layer information
    const layerSources = [
      element.attributes['data-layer'],
      element.attributes['layer'],
      element.attributes['class'],
      element.id
    ];
    
    for (const source of layerSources) {
      if (source) {
        // Extract layer name from class or id
        const match = source.match(/layer-([^\\s]+)/);
        if (match) {
          return match[1];
        }
        return source;
      }
    }
    
    return 'default';
  }

  /**
   * Check if layer should be skipped based on filtering options
   */
  private shouldSkipLayer(layerName: string, options: SVGImportOptions): boolean {
    const filtering = options.layerFiltering;
    if (!filtering) return false;
    
    // Skip LMM layers if disabled
    if (!filtering.showLMM && layerName.startsWith('LMM')) {
      return true;
    }
    
    // Skip labels if disabled
    if (!filtering.showLabels && layerName.toLowerCase().includes('label')) {
      return true;
    }
    
    // Skip parameters if disabled
    if (!filtering.showParameters && layerName.toLowerCase().includes('parameter')) {
      return true;
    }
    
    // Check visible layers whitelist
    if (filtering.visibleLayers && !filtering.visibleLayers.includes(layerName)) {
      return true;
    }
    
    return false;
  }

  /**
   * Create CAD/CAM layer from layer name and elements
   */
  private createCADCAMLayer(layerName: string, elements: ParsedSVGElement[], options: SVGImportOptions): CADCAMLayer | null {
    // Determine operation type from layer name
    const operationType = this.detectOperationType(layerName);
    
    // Determine face (top/bottom)
    const face = this.detectFace(layerName, options.face);
    
    // Detect tool information
    const toolInfo = this.layerDetector.detectTool(layerName);
    
    // Calculate Z offset based on operation type and face
    const zOffset = this.calculateZOffset(operationType, face, options);
    
    // Add CAD/CAM properties to elements
    elements.forEach(element => {
      element.face = face;
      element.operationType = operationType;
      element.toolInfo = toolInfo;
      element.machiningParams = {
        depth: this.calculateOperationDepth(operationType, options),
        startZ: zOffset,
        feedRate: this.getDefaultFeedRate(operationType),
        spindleSpeed: this.getDefaultSpindleSpeed(operationType)
      };
    });
    
    return {
      name: layerName,
      face,
      operationType,
      toolInfo,
      visible: true,
      elements,
      zOffset
    };
  }

  /**
   * Detect operation type from layer name
   */
  private detectOperationType(layerName: string): 'groove' | 'pocket' | 'drill' | 'v-carve' | 'chamfer' | 'fillet' | 'panel' {
    const name = layerName.toLowerCase();
    
    if (name === 'panel') return 'panel';
    if (name.includes('groove') || name.startsWith('k')) return 'groove'; // K = Kanal (Groove)
    if (name.includes('pocket')) return 'pocket';
    if (name.includes('drill') || name.includes('hole')) return 'drill';
    if (name.includes('v-') || name.startsWith('v') || name.includes('conical')) return 'v-carve';
    if (name.includes('chamfer')) return 'chamfer';
    if (name.includes('fillet') || name.includes('round')) return 'fillet';
    
    // Default based on tool type
    if (name.includes('mm') && /\\d+mm/i.test(name)) {
      return 'groove'; // Numeric layers are typically grooves
    }
    
    return 'groove'; // Default operation
  }

  /**
   * Detect which face the operation belongs to
   */
  private detectFace(layerName: string, defaultFace?: 'top' | 'bottom' | 'both'): 'top' | 'bottom' {
    const name = layerName.toLowerCase();
    
    if (name.includes('top') || name.includes('ust') || name.includes('üst')) return 'top';
    if (name.includes('bottom') || name.includes('alt')) return 'bottom';
    
    // Use default face if specified
    if (defaultFace === 'top' || defaultFace === 'bottom') {
      return defaultFace;
    }
    
    // Default to top face for most operations
    return 'top';
  }

  /**
   * Calculate Z offset for operation
   */
  private calculateZOffset(operationType: string, face: 'top' | 'bottom', options: SVGImportOptions): number {
    const startingZ = options.startingZ || 0;
    
    switch (face) {
      case 'top':
        return startingZ; // Start from top surface
      case 'bottom':
        return startingZ - this.panelThickness; // Start from bottom surface
      default:
        return startingZ;
    }
  }

  /**
   * Calculate operation depth
   */
  private calculateOperationDepth(operationType: string, options: SVGImportOptions): number {
    const defaultDepth = options.depth || 5;
    
    switch (operationType) {
      case 'groove':
        return Math.min(defaultDepth, this.panelThickness * 0.6); // Max 60% of panel thickness
      case 'pocket':
        return Math.min(defaultDepth, this.panelThickness * 0.8); // Max 80% of panel thickness
      case 'drill':
        return this.panelThickness; // Through hole
      case 'v-carve':
        return Math.min(defaultDepth, this.panelThickness * 0.4); // Shallow V-carve
      case 'chamfer':
        return Math.min(2, this.panelThickness * 0.1); // Small chamfer
      case 'fillet':
        return Math.min(1, this.panelThickness * 0.05); // Small fillet
      default:
        return defaultDepth;
    }
  }

  /**
   * Create default panel layer
   */
  private createDefaultPanelLayer(): CADCAMLayer {
    return {
      name: 'PANEL',
      face: 'top',
      operationType: 'panel',
      visible: true,
      elements: [],
      zOffset: 0
    };
  }

  /**
   * Get default feed rate for operation type
   */
  private getDefaultFeedRate(operationType: string): number {
    switch (operationType) {
      case 'groove': return 1000; // mm/min
      case 'pocket': return 800;
      case 'drill': return 300;
      case 'v-carve': return 600;
      case 'chamfer': return 1200;
      case 'fillet': return 1200;
      default: return 800;
    }
  }

  /**
   * Get default spindle speed for operation type
   */
  private getDefaultSpindleSpeed(operationType: string): number {
    switch (operationType) {
      case 'groove': return 18000; // RPM
      case 'pocket': return 16000;
      case 'drill': return 12000;
      case 'v-carve': return 20000;
      case 'chamfer': return 18000;
      case 'fillet': return 18000;
      default: return 16000;
    }
  }
}

/**
 * Layer Tool Detector - matches your existing layerToolDetector service
 */
class LayerToolDetector {
  detectTool(layerName: string): { diameter: number; type: 'endmill' | 'v-bit' | 'ballnose'; radius: number } | undefined {
    const name = layerName.toLowerCase();
    
    // Extract diameter from numeric layers (20MM, 30MM, etc.)
    const diameterMatch = name.match(/(\\d+)mm/);
    if (diameterMatch) {
      const diameter = parseInt(diameterMatch[1]);
      return {
        diameter,
        type: 'endmill',
        radius: diameter / 2
      };
    }
    
    // Detect V-bit tools
    if (name.includes('v') || name.includes('conical')) {
      const angleMatch = name.match(/v(\\d+)/);
      const angle = angleMatch ? parseInt(angleMatch[1]) : 90;
      return {
        diameter: 6, // Default V-bit diameter
        type: 'v-bit',
        radius: 3
      };
    }
    
    // Detect ballnose tools
    if (name.includes('ball') || name.includes('round')) {
      return {
        diameter: 6,
        type: 'ballnose',
        radius: 3
      };
    }
    
    return undefined;
  }
}
