<template>
  <div class="zdog-controls">
    <!-- Toolbar -->
    <div class="zdog-toolbar">
      <div class="toolbar-section">
        <button
          @click="toggleAutoRotate"
          :class="{ active: controls.autoRotate }"
          class="toolbar-button"
          title="Toggle Auto Rotation"
        >
          <RotateCcw :size="16" />
        </button>
        
        <button
          @click="toggleWireframe"
          :class="{ active: controls.showWireframe }"
          class="toolbar-button"
          title="Toggle Wireframe"
        >
          <Grid :size="16" />
        </button>
        
        <button
          @click="resetView"
          class="toolbar-button"
          title="Reset View"
        >
          <RotateCcw :size="16" />
        </button>
        
        <button
          @click="exportImage"
          class="toolbar-button"
          title="Export as PNG"
        >
          <Download :size="16" />
        </button>
      </div>
      
      <div class="toolbar-section">
        <span class="fps-counter">{{ fps }} FPS</span>
      </div>
    </div>

    <!-- Control Panels -->
    <div class="control-panels">
      <!-- Transform Controls -->
      <div class="control-panel">
        <h3 class="panel-title">Transform</h3>
        
        <div class="control-group">
          <label class="control-label">Depth/Extrusion</label>
          <div class="slider-container">
            <input
              type="range"
              min="0"
              max="100"
              step="1"
              v-model.number="controls.depth"
              @input="onDepthChange"
              class="slider"
            />
            <span class="slider-value">{{ controls.depth }}</span>
          </div>
        </div>
        
        <div class="control-group">
          <label class="control-label">Stroke Width</label>
          <div class="slider-container">
            <input
              type="range"
              min="1"
              max="50"
              step="1"
              v-model.number="controls.strokeWidth"
              @input="onStrokeWidthChange"
              class="slider"
            />
            <span class="slider-value">{{ controls.strokeWidth }}</span>
          </div>
        </div>
        
        <div class="control-group">
          <label class="control-label">Zoom</label>
          <div class="slider-container">
            <input
              type="range"
              min="0.1"
              max="5"
              step="0.1"
              v-model.number="controls.zoom"
              @input="onZoomChange"
              class="slider"
            />
            <span class="slider-value">{{ controls.zoom.toFixed(1) }}</span>
          </div>
        </div>
      </div>

      <!-- Animation Controls -->
      <div class="control-panel">
        <h3 class="panel-title">Animation</h3>
        
        <div class="control-group">
          <label class="control-label">
            <input
              type="checkbox"
              v-model="controls.autoRotate"
              @change="onAutoRotateChange"
              class="checkbox"
            />
            Auto Rotate
          </label>
        </div>
        
        <div class="control-group" v-if="controls.autoRotate">
          <label class="control-label">Rotation Speed</label>
          <div class="slider-container">
            <input
              type="range"
              min="0.001"
              max="0.1"
              step="0.001"
              v-model.number="controls.rotationSpeed"
              @input="onRotationSpeedChange"
              class="slider"
            />
            <span class="slider-value">{{ (controls.rotationSpeed * 1000).toFixed(0) }}</span>
          </div>
        </div>
      </div>

      <!-- Rendering Controls -->
      <div class="control-panel">
        <h3 class="panel-title">Rendering</h3>
        
        <div class="control-group">
          <label class="control-label">
            <input
              type="checkbox"
              v-model="controls.showWireframe"
              @change="onWireframeChange"
              class="checkbox"
            />
            Wireframe Mode
          </label>
        </div>
        
        <div class="control-group">
          <label class="control-label">
            <input
              type="checkbox"
              v-model="controls.enableShadows"
              @change="onShadowsChange"
              class="checkbox"
            />
            Enable Shadows
          </label>
        </div>
      </div>

      <!-- Export Controls -->
      <div class="control-panel">
        <h3 class="panel-title">Export</h3>
        
        <div class="control-group">
          <label class="control-label">Format</label>
          <select v-model="exportFormat" class="select">
            <option value="png">PNG</option>
            <option value="webp">WebP</option>
            <option value="svg">SVG</option>
          </select>
        </div>
        
        <div class="control-group">
          <label class="control-label">Quality</label>
          <div class="slider-container">
            <input
              type="range"
              min="0.1"
              max="1"
              step="0.1"
              v-model.number="exportQuality"
              class="slider"
            />
            <span class="slider-value">{{ (exportQuality * 100).toFixed(0) }}%</span>
          </div>
        </div>
        
        <div class="control-group">
          <button @click="exportImage" class="export-button">
            <Download :size="16" />
            Export Image
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, onUnmounted } from 'vue';
import { RotateCcw, Grid, Download } from 'lucide-vue-next';
import type { ViewerControls, ExportOptions } from './types/viewer-types';
import type { ZdogSVGViewer } from './ZdogViewer';

interface Props {
  viewer: ZdogSVGViewer;
}

interface Emits {
  (e: 'export', options: ExportOptions): void;
  (e: 'controls-change', controls: ViewerControls): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Reactive state
const controls = reactive<ViewerControls>({
  autoRotate: false,
  rotationSpeed: 0.01,
  zoom: 1,
  panX: 0,
  panY: 0,
  depth: 20,
  strokeWidth: 2,
  showWireframe: false,
  enableShadows: false
});

const fps = ref(0);
const exportFormat = ref<'png' | 'webp' | 'svg'>('png');
const exportQuality = ref(0.9);

// Event handlers
const toggleAutoRotate = (): void => {
  controls.autoRotate = !controls.autoRotate;
  onAutoRotateChange();
};

const toggleWireframe = (): void => {
  controls.showWireframe = !controls.showWireframe;
  onWireframeChange();
};

const resetView = (): void => {
  controls.zoom = 1;
  controls.panX = 0;
  controls.panY = 0;
  // Reset viewer rotation
  if (props.viewer && props.viewer.illustration) {
    props.viewer.illustration.rotate.set({ x: 0, y: 0, z: 0 });
  }
  emitControlsChange();
};

const exportImage = async (): Promise<void> => {
  const options: ExportOptions = {
    format: exportFormat.value,
    quality: exportQuality.value,
    scale: 1
  };
  
  emit('export', options);
  
  try {
    const blob = await props.viewer.exportImage(options);
    
    // Download the exported image
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `zdog-export.${exportFormat.value}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Export failed:', error);
  }
};

// Control change handlers
const onDepthChange = (): void => {
  // Update all shapes with new depth
  updateAllShapes();
  emitControlsChange();
};

const onStrokeWidthChange = (): void => {
  // Update all shapes with new stroke width
  updateAllShapes();
  emitControlsChange();
};

const onZoomChange = (): void => {
  if (props.viewer && props.viewer.illustration) {
    props.viewer.illustration.zoom = controls.zoom;
  }
  emitControlsChange();
};

const onAutoRotateChange = (): void => {
  emitControlsChange();
};

const onRotationSpeedChange = (): void => {
  emitControlsChange();
};

const onWireframeChange = (): void => {
  // Update rendering mode
  updateAllShapes();
  emitControlsChange();
};

const onShadowsChange = (): void => {
  // Update shadow rendering
  emitControlsChange();
};

const updateAllShapes = (): void => {
  if (!props.viewer) return;
  
  // Update all shapes with current control values
  for (const [id, zdogShape] of props.viewer.state.shapes) {
    props.viewer.updateShape(id, {
      stroke: controls.showWireframe ? controls.strokeWidth : 0,
      fill: !controls.showWireframe
    });
  }
};

const emitControlsChange = (): void => {
  emit('controls-change', { ...controls });
};

// Setup FPS monitoring
const setupFPSMonitoring = (): void => {
  if (!props.viewer) return;
  
  props.viewer.on('render', (data) => {
    fps.value = data.fps;
  });
};

// Lifecycle
onMounted(() => {
  setupFPSMonitoring();
  
  // Initialize controls from viewer state
  const viewerState = props.viewer.state;
  Object.assign(controls, viewerState.controls);
});

onUnmounted(() => {
  // Cleanup if needed
});

// Watch for external control changes
watch(() => props.viewer.state.controls, (newControls) => {
  Object.assign(controls, newControls);
}, { deep: true });
</script>

<style scoped lang="scss">
.zdog-controls {
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  border-left: 1px solid #e9ecef;
  min-width: 280px;
  max-width: 320px;
}

.zdog-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 4px;
}

.toolbar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #dee2e6;
  background: #ffffff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
  }
  
  &.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
  }
}

.fps-counter {
  font-size: 12px;
  font-weight: 600;
  color: #6c757d;
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.control-panels {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.control-panel {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.panel-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #f1f3f4;
}

.control-group {
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.control-label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #6c757d;
  margin-bottom: 6px;
  
  &:has(.checkbox) {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 0;
  }
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.slider {
  flex: 1;
  height: 4px;
  border-radius: 2px;
  background: #e9ecef;
  outline: none;
  cursor: pointer;
  
  &::-webkit-slider-thumb {
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }
  
  &::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }
}

.slider-value {
  font-size: 11px;
  font-weight: 600;
  color: #495057;
  min-width: 32px;
  text-align: right;
}

.checkbox {
  width: 16px;
  height: 16px;
  accent-color: #007bff;
}

.select {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: #ffffff;
  font-size: 12px;
  color: #495057;
  
  &:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }
}

.export-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 8px 12px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background: #218838;
  }
  
  &:active {
    background: #1e7e34;
  }
}
</style>
