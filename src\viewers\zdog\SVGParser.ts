/**
 * SVG Parser for Zdog 3D SVG Viewer
 * Parses SVG elements and converts them to Zdog-compatible format
 */

import type {
  SVGPathCommand,
  SVGPoint,
  SVGBounds,
  SVGTransform,
  SVGStyle,
  ParsedSVGElement,
  SVGParseOptions,
  SVGParseResult,
  SVGParseError,
  ParsedPathSegment
} from './types/svg-types';
import { parseColor } from '@/utils/color';

export class SVGParser {
  private options: Required<SVGParseOptions>;
  private errors: SVGParseError[] = [];

  constructor(options: SVGParseOptions = {}) {
    this.options = {
      preserveAspectRatio: options.preserveAspectRatio ?? true,
      ignoreInvisible: options.ignoreInvisible ?? true,
      flattenGroups: options.flattenGroups ?? false,
      simplifyPaths: options.simplifyPaths ?? true,
      tolerance: options.tolerance ?? 0.1
    };
  }

  /**
   * Parse SVG content string
   */
  public parseSVG(svgContent: string): SVGParseResult {
    this.errors = [];
    
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(svgContent, 'image/svg+xml');
      
      // Check for parsing errors
      const parserError = doc.querySelector('parsererror');
      if (parserError) {
        throw new SVGParseError('Invalid SVG content', 1, 1);
      }

      const svgElement = doc.querySelector('svg');
      if (!svgElement) {
        throw new SVGParseError('No SVG root element found');
      }

      const viewBox = this.parseViewBox(svgElement);
      const { width, height } = this.parseDimensions(svgElement, viewBox);
      const elements = this.parseElement(svgElement);

      return {
        elements: elements.children || [],
        viewBox,
        width,
        height,
        errors: this.errors
      };
    } catch (error) {
      if (error instanceof SVGParseError) {
        this.errors.push(error);
      } else {
        this.errors.push(new SVGParseError(`Unexpected error: ${error}`));
      }
      
      return {
        elements: [],
        viewBox: { x: 0, y: 0, width: 100, height: 100 },
        width: 100,
        height: 100,
        errors: this.errors
      };
    }
  }

  /**
   * Parse individual SVG element
   */
  public parseElement(element: Element): ParsedSVGElement {
    const tagName = element.tagName.toLowerCase();
    const attributes = this.extractAttributes(element);
    const style = this.extractStyle(element);
    const transform = this.parseTransform(attributes.transform || '');

    // Skip invisible elements if option is set
    if (this.options.ignoreInvisible && this.isInvisible(style)) {
      return {
        type: 'g',
        attributes,
        style,
        transform,
        children: []
      };
    }

    const baseElement: ParsedSVGElement = {
      id: attributes.id,
      type: tagName as ParsedSVGElement['type'],
      attributes,
      style,
      transform
    };

    switch (tagName) {
      case 'path':
        return this.parsePath(baseElement, element);
      case 'circle':
        return this.parseCircle(baseElement, element);
      case 'rect':
        return this.parseRect(baseElement, element);
      case 'ellipse':
        return this.parseEllipse(baseElement, element);
      case 'polygon':
        return this.parsePolygon(baseElement, element);
      case 'polyline':
        return this.parsePolyline(baseElement, element);
      case 'line':
        return this.parseLine(baseElement, element);
      case 'g':
        return this.parseGroup(baseElement, element);
      default:
        this.addError(`Unsupported element type: ${tagName}`, element);
        return baseElement;
    }
  }

  /**
   * Parse SVG path element
   */
  private parsePath(baseElement: ParsedSVGElement, element: Element): ParsedSVGElement {
    const pathData = element.getAttribute('d') || '';
    
    try {
      const commands = this.parsePathData(pathData);
      const bounds = this.calculatePathBounds(commands);
      
      return {
        ...baseElement,
        type: 'path',
        pathData: this.options.simplifyPaths ? this.simplifyPath(pathData) : pathData,
        bounds
      };
    } catch (error) {
      this.addError(`Failed to parse path data: ${error}`, element);
      return baseElement;
    }
  }

  /**
   * Parse SVG path data string into commands
   */
  public parsePathData(pathData: string): SVGPathCommand[] {
    const commands: SVGPathCommand[] = [];
    const commandRegex = /([MmLlHhVvCcSsQqTtAaZz])((?:\s*[-+]?(?:\d+\.?\d*|\.\d+)(?:[eE][-+]?\d+)?\s*,?\s*)*)/g;
    
    let match;
    while ((match = commandRegex.exec(pathData)) !== null) {
      const [, command, argsString] = match;
      const args = this.parseNumbers(argsString);
      
      const isAbsolute = command === command.toUpperCase();
      const commandType = command.toUpperCase() as SVGPathCommand['type'];
      
      // Handle different command types
      switch (commandType) {
        case 'M':
        case 'L':
          for (let i = 0; i < args.length; i += 2) {
            if (i + 1 < args.length) {
              commands.push({
                type: commandType,
                points: [args[i], args[i + 1]],
                absolute: isAbsolute
              });
            }
          }
          break;
          
        case 'H':
          for (const arg of args) {
            commands.push({
              type: commandType,
              points: [arg],
              absolute: isAbsolute
            });
          }
          break;
          
        case 'V':
          for (const arg of args) {
            commands.push({
              type: commandType,
              points: [arg],
              absolute: isAbsolute
            });
          }
          break;
          
        case 'C':
          for (let i = 0; i < args.length; i += 6) {
            if (i + 5 < args.length) {
              commands.push({
                type: commandType,
                points: args.slice(i, i + 6),
                absolute: isAbsolute
              });
            }
          }
          break;
          
        case 'S':
        case 'Q':
          for (let i = 0; i < args.length; i += 4) {
            if (i + 3 < args.length) {
              commands.push({
                type: commandType,
                points: args.slice(i, i + 4),
                absolute: isAbsolute
              });
            }
          }
          break;
          
        case 'T':
          for (let i = 0; i < args.length; i += 2) {
            if (i + 1 < args.length) {
              commands.push({
                type: commandType,
                points: [args[i], args[i + 1]],
                absolute: isAbsolute
              });
            }
          }
          break;
          
        case 'A':
          for (let i = 0; i < args.length; i += 7) {
            if (i + 6 < args.length) {
              commands.push({
                type: commandType,
                points: args.slice(i, i + 7),
                absolute: isAbsolute
              });
            }
          }
          break;
          
        case 'Z':
          commands.push({
            type: commandType,
            points: [],
            absolute: isAbsolute
          });
          break;
      }
    }
    
    return commands;
  }

  /**
   * Parse numbers from string
   */
  private parseNumbers(str: string): number[] {
    const numberRegex = /[-+]?(?:\d+\.?\d*|\.\d+)(?:[eE][-+]?\d+)?/g;
    const matches = str.match(numberRegex);
    return matches ? matches.map(Number) : [];
  }

  /**
   * Parse circle element
   */
  private parseCircle(baseElement: ParsedSVGElement, element: Element): ParsedSVGElement {
    const cx = parseFloat(element.getAttribute('cx') || '0');
    const cy = parseFloat(element.getAttribute('cy') || '0');
    const r = parseFloat(element.getAttribute('r') || '0');
    
    return {
      ...baseElement,
      type: 'circle',
      bounds: {
        x: cx - r,
        y: cy - r,
        width: r * 2,
        height: r * 2
      }
    };
  }

  /**
   * Parse rectangle element
   */
  private parseRect(baseElement: ParsedSVGElement, element: Element): ParsedSVGElement {
    const x = parseFloat(element.getAttribute('x') || '0');
    const y = parseFloat(element.getAttribute('y') || '0');
    const width = parseFloat(element.getAttribute('width') || '0');
    const height = parseFloat(element.getAttribute('height') || '0');
    
    return {
      ...baseElement,
      type: 'rect',
      bounds: { x, y, width, height }
    };
  }

  /**
   * Parse group element
   */
  private parseGroup(baseElement: ParsedSVGElement, element: Element): ParsedSVGElement {
    const children: ParsedSVGElement[] = [];
    
    for (const child of Array.from(element.children)) {
      const parsedChild = this.parseElement(child);
      children.push(parsedChild);
    }
    
    return {
      ...baseElement,
      type: 'g',
      children
    };
  }

  /**
   * Extract attributes from element
   */
  private extractAttributes(element: Element): Record<string, string> {
    const attributes: Record<string, string> = {};
    
    for (const attr of Array.from(element.attributes)) {
      attributes[attr.name] = attr.value;
    }
    
    return attributes;
  }

  /**
   * Extract style from element
   */
  private extractStyle(element: Element): SVGStyle {
    const style: SVGStyle = {};
    
    // Parse style attribute
    const styleAttr = element.getAttribute('style');
    if (styleAttr) {
      const declarations = styleAttr.split(';');
      for (const declaration of declarations) {
        const [property, value] = declaration.split(':').map(s => s.trim());
        if (property && value) {
          this.setStyleProperty(style, property, value);
        }
      }
    }
    
    // Parse individual style attributes
    const styleAttributes = ['fill', 'stroke', 'stroke-width', 'opacity'];
    for (const attr of styleAttributes) {
      const value = element.getAttribute(attr);
      if (value) {
        this.setStyleProperty(style, attr, value);
      }
    }
    
    return style;
  }

  /**
   * Set style property with proper typing
   */
  private setStyleProperty(style: SVGStyle, property: string, value: string): void {
    switch (property) {
      case 'fill':
        style.fill = value;
        break;
      case 'stroke':
        style.stroke = value;
        break;
      case 'stroke-width':
        style.strokeWidth = parseFloat(value);
        break;
      case 'opacity':
        style.opacity = parseFloat(value);
        break;
      // Add more style properties as needed
    }
  }

  /**
   * Parse transform attribute
   */
  private parseTransform(transformString: string): SVGTransform[] {
    const transforms: SVGTransform[] = [];
    const transformRegex = /(\w+)\s*\(([^)]*)\)/g;
    
    let match;
    while ((match = transformRegex.exec(transformString)) !== null) {
      const [, type, valuesString] = match;
      const values = this.parseNumbers(valuesString);
      
      transforms.push({
        type: type as SVGTransform['type'],
        values
      });
    }
    
    return transforms;
  }

  /**
   * Parse viewBox attribute
   */
  private parseViewBox(svgElement: Element): SVGBounds {
    const viewBox = svgElement.getAttribute('viewBox');
    if (viewBox) {
      const values = this.parseNumbers(viewBox);
      if (values.length === 4) {
        return {
          x: values[0],
          y: values[1],
          width: values[2],
          height: values[3]
        };
      }
    }
    
    return { x: 0, y: 0, width: 100, height: 100 };
  }

  /**
   * Parse SVG dimensions
   */
  private parseDimensions(svgElement: Element, viewBox: SVGBounds): { width: number; height: number } {
    const width = parseFloat(svgElement.getAttribute('width') || viewBox.width.toString());
    const height = parseFloat(svgElement.getAttribute('height') || viewBox.height.toString());
    
    return { width, height };
  }

  /**
   * Check if element is invisible
   */
  private isInvisible(style: SVGStyle): boolean {
    return style.opacity === 0 || 
           style.fill === 'none' && (!style.stroke || style.stroke === 'none');
  }

  /**
   * Calculate bounds for path commands
   */
  private calculatePathBounds(commands: SVGPathCommand[]): SVGBounds {
    let minX = Infinity, minY = Infinity;
    let maxX = -Infinity, maxY = -Infinity;
    let currentX = 0, currentY = 0;
    
    for (const command of commands) {
      const points = command.points;
      
      switch (command.type) {
        case 'M':
        case 'L':
          if (points.length >= 2) {
            currentX = command.absolute ? points[0] : currentX + points[0];
            currentY = command.absolute ? points[1] : currentY + points[1];
            minX = Math.min(minX, currentX);
            minY = Math.min(minY, currentY);
            maxX = Math.max(maxX, currentX);
            maxY = Math.max(maxY, currentY);
          }
          break;
        // Add more command types as needed
      }
    }
    
    if (minX === Infinity) {
      return { x: 0, y: 0, width: 0, height: 0 };
    }
    
    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    };
  }

  /**
   * Simplify path data
   */
  private simplifyPath(pathData: string): string {
    // Basic path simplification - remove unnecessary precision
    return pathData.replace(/(\d+\.\d{3})\d+/g, '$1');
  }

  /**
   * Add error to error list
   */
  private addError(message: string, element?: Element): void {
    this.errors.push(new SVGParseError(message, undefined, undefined, element?.tagName));
  }

  // Additional parsing methods for other SVG elements would go here...
  private parseEllipse(baseElement: ParsedSVGElement, element: Element): ParsedSVGElement {
    // Implementation for ellipse parsing
    return baseElement;
  }

  private parsePolygon(baseElement: ParsedSVGElement, element: Element): ParsedSVGElement {
    // Implementation for polygon parsing
    return baseElement;
  }

  private parsePolyline(baseElement: ParsedSVGElement, element: Element): ParsedSVGElement {
    // Implementation for polyline parsing
    return baseElement;
  }

  private parseLine(baseElement: ParsedSVGElement, element: Element): ParsedSVGElement {
    // Implementation for line parsing
    return baseElement;
  }
}
