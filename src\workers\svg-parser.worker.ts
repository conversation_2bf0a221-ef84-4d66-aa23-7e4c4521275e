/**
 * Web Worker for SVG parsing to improve performance for large SVG files
 */

import { SVGParser } from '../viewers/zdog/SVGParser';
import type { 
  SVGParseOptions, 
  SVGParseResult, 
  ParsedSVGElement 
} from '../viewers/zdog/types/svg-types';

export interface SVGParseMessage {
  id: string;
  type: 'parse' | 'optimize' | 'validate';
  data: {
    svgContent?: string;
    options?: SVGParseOptions;
    elements?: ParsedSVGElement[];
  };
}

export interface SVGParseResponse {
  id: string;
  type: 'success' | 'error' | 'progress';
  data: {
    result?: SVGParseResult;
    optimizedElements?: ParsedSVGElement[];
    isValid?: boolean;
    progress?: number;
    error?: string;
  };
}

class SVGParserWorker {
  private parser: SVGParser;
  private currentTaskId: string | null = null;

  constructor() {
    this.parser = new SVGParser();
    this.setupMessageHandler();
  }

  private setupMessageHandler(): void {
    self.addEventListener('message', (event: MessageEvent<SVGParseMessage>) => {
      const { id, type, data } = event.data;
      this.currentTaskId = id;

      try {
        switch (type) {
          case 'parse':
            this.handleParse(id, data.svgContent!, data.options);
            break;
          case 'optimize':
            this.handleOptimize(id, data.elements!);
            break;
          case 'validate':
            this.handleValidate(id, data.svgContent!);
            break;
          default:
            this.sendError(id, `Unknown message type: ${type}`);
        }
      } catch (error) {
        this.sendError(id, `Worker error: ${error}`);
      }
    });
  }

  private async handleParse(id: string, svgContent: string, options?: SVGParseOptions): Promise<void> {
    try {
      // Send progress update
      this.sendProgress(id, 10);

      // Configure parser with options
      if (options) {
        this.parser = new SVGParser(options);
      }

      this.sendProgress(id, 30);

      // Parse SVG content
      const result = this.parser.parseSVG(svgContent);

      this.sendProgress(id, 80);

      // Perform additional optimizations for large files
      if (result.elements.length > 1000) {
        result.elements = this.optimizeElements(result.elements);
      }

      this.sendProgress(id, 100);

      // Send successful result
      this.sendSuccess(id, { result });

    } catch (error) {
      this.sendError(id, `Parse error: ${error}`);
    }
  }

  private async handleOptimize(id: string, elements: ParsedSVGElement[]): Promise<void> {
    try {
      this.sendProgress(id, 10);

      const optimizedElements = this.optimizeElements(elements);

      this.sendProgress(id, 100);

      this.sendSuccess(id, { optimizedElements });

    } catch (error) {
      this.sendError(id, `Optimization error: ${error}`);
    }
  }

  private async handleValidate(id: string, svgContent: string): Promise<void> {
    try {
      // Basic SVG validation
      const isValid = this.validateSVG(svgContent);
      
      this.sendSuccess(id, { isValid });

    } catch (error) {
      this.sendError(id, `Validation error: ${error}`);
    }
  }

  private optimizeElements(elements: ParsedSVGElement[]): ParsedSVGElement[] {
    const optimized: ParsedSVGElement[] = [];
    
    for (const element of elements) {
      // Skip invisible elements
      if (this.isElementVisible(element)) {
        // Simplify paths if they're too complex
        if (element.type === 'path' && element.pathData) {
          element.pathData = this.simplifyPath(element.pathData);
        }
        
        // Merge similar elements
        const merged = this.tryMergeWithPrevious(element, optimized);
        if (!merged) {
          optimized.push(element);
        }
      }
    }
    
    return optimized;
  }

  private isElementVisible(element: ParsedSVGElement): boolean {
    const style = element.style;
    
    // Check if element is explicitly hidden
    if (style.opacity === 0) return false;
    if (style.fill === 'none' && (!style.stroke || style.stroke === 'none')) return false;
    
    // Check if element has zero dimensions
    if (element.bounds) {
      if (element.bounds.width === 0 || element.bounds.height === 0) return false;
    }
    
    return true;
  }

  private simplifyPath(pathData: string): string {
    // Basic path simplification - remove unnecessary precision
    return pathData
      .replace(/(\d+\.\d{3})\d+/g, '$1') // Limit decimal places to 3
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  }

  private tryMergeWithPrevious(element: ParsedSVGElement, optimized: ParsedSVGElement[]): boolean {
    if (optimized.length === 0) return false;
    
    const previous = optimized[optimized.length - 1];
    
    // Only merge elements of the same type with similar styles
    if (element.type !== previous.type) return false;
    if (!this.haveSimilarStyles(element.style, previous.style)) return false;
    
    // For now, we don't actually merge - just return false
    // In a real implementation, you might merge paths or group similar elements
    return false;
  }

  private haveSimilarStyles(style1: any, style2: any): boolean {
    return style1.fill === style2.fill && 
           style1.stroke === style2.stroke && 
           style1.strokeWidth === style2.strokeWidth;
  }

  private validateSVG(svgContent: string): boolean {
    try {
      // Basic validation - check if it's valid XML and has SVG root
      const parser = new DOMParser();
      const doc = parser.parseFromString(svgContent, 'image/svg+xml');
      
      // Check for parser errors
      const parserError = doc.querySelector('parsererror');
      if (parserError) return false;
      
      // Check for SVG root element
      const svgElement = doc.querySelector('svg');
      if (!svgElement) return false;
      
      return true;
    } catch {
      return false;
    }
  }

  private sendSuccess(id: string, data: any): void {
    const response: SVGParseResponse = {
      id,
      type: 'success',
      data
    };
    self.postMessage(response);
  }

  private sendError(id: string, error: string): void {
    const response: SVGParseResponse = {
      id,
      type: 'error',
      data: { error }
    };
    self.postMessage(response);
  }

  private sendProgress(id: string, progress: number): void {
    const response: SVGParseResponse = {
      id,
      type: 'progress',
      data: { progress }
    };
    self.postMessage(response);
  }
}

// Initialize the worker
new SVGParserWorker();

// Export types for TypeScript
export type { SVGParseMessage, SVGParseResponse };
