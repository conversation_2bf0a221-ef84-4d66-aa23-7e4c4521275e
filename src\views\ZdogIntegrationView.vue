<template>
  <div class="zdog-integration-view">
    <!-- Header with quick actions -->
    <div class="integration-header">
      <div class="header-content">
        <h2>3D SVG Visualization</h2>
        <p>Convert your SVG content to interactive 3D models</p>
      </div>
      
      <div class="header-actions">
        <button @click="openZdogViewer" class="action-button primary">
          <Box :size="16" />
          Open Zdog Viewer
        </button>
        
        <button @click="openCADCAMViewer" class="action-button secondary">
          <Layers :size="16" />
          Open CAD/CAM Viewer
        </button>
      </div>
    </div>

    <!-- Quick Integration Panel -->
    <div class="integration-panel">
      <div class="panel-section">
        <h3>Quick SVG to 3D</h3>
        <p>Paste your SVG content below to see it in 3D:</p>
        
        <div class="svg-input-section">
          <textarea
            v-model="svgContent"
            placeholder="Paste SVG content here..."
            class="svg-textarea"
            rows="8"
          ></textarea>
          
          <div class="input-actions">
            <button 
              @click="loadInZdog" 
              :disabled="!svgContent.trim()"
              class="load-button"
            >
              <Play :size="16" />
              Load in 3D Viewer
            </button>
            
            <button 
              @click="loadInCADCAM" 
              :disabled="!svgContent.trim()"
              class="load-button cadcam"
            >
              <Settings :size="16" />
              Load in CAD/CAM Viewer
            </button>
          </div>
        </div>
      </div>

      <div class="panel-section">
        <h3>Current Editor Content</h3>
        <p>Use the current editor content for 3D visualization:</p>
        
        <div class="editor-actions">
          <button @click="useEditorContent" class="editor-button">
            <FileText :size="16" />
            Use Current Editor Content
          </button>
          
          <div v-if="hasEditorContent" class="editor-info">
            <span class="info-text">
              Editor has {{ editorLineCount }} lines of content
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Feature Overview -->
    <div class="features-section">
      <h3>Available Viewers</h3>
      
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">
            <Box :size="32" />
          </div>
          <h4>Zdog 3D Viewer</h4>
          <p>General purpose SVG to 3D converter with interactive controls</p>
          <ul class="feature-list">
            <li>Interactive 3D rotation</li>
            <li>Depth/extrusion controls</li>
            <li>Export to PNG/WebP</li>
            <li>Real-time preview</li>
          </ul>
          <button @click="openZdogViewer" class="feature-button">
            Open Viewer
          </button>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <Layers :size="32" />
          </div>
          <h4>CAD/CAM Manufacturing</h4>
          <p>Specialized viewer for door manufacturing with face operations</p>
          <ul class="feature-list">
            <li>Top/Bottom face operations</li>
            <li>Layer management</li>
            <li>Tool path visualization</li>
            <li>Manufacturing operations</li>
          </ul>
          <button @click="openCADCAMViewer" class="feature-button">
            Open CAD/CAM Viewer
          </button>
        </div>
      </div>
    </div>

    <!-- Modal for viewer selection -->
    <div v-if="showViewerModal" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>Choose Viewer Type</h3>
          <button @click="closeModal" class="close-button">
            <X :size="16" />
          </button>
        </div>
        
        <div class="modal-body">
          <p>Which viewer would you like to use for this content?</p>
          
          <div class="viewer-options">
            <button @click="confirmZdogViewer" class="viewer-option">
              <Box :size="24" />
              <span>Zdog 3D Viewer</span>
              <small>General SVG visualization</small>
            </button>
            
            <button @click="confirmCADCAMViewer" class="viewer-option">
              <Layers :size="24" />
              <span>CAD/CAM Viewer</span>
              <small>Door manufacturing operations</small>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject } from 'vue';
import { 
  Box, 
  Layers, 
  Play, 
  Settings, 
  FileText, 
  X 
} from 'lucide-vue-next';

// Inject editor state if available
const editorState = inject('editorState', null);

// State
const svgContent = ref('');
const showViewerModal = ref(false);
const pendingContent = ref('');

// Computed
const hasEditorContent = computed(() => {
  return editorState?.content && editorState.content.trim().length > 0;
});

const editorLineCount = computed(() => {
  if (!hasEditorContent.value) return 0;
  return editorState.content.split('\n').length;
});

// Methods
const openZdogViewer = (): void => {
  // Emit event to parent to switch to zdog viewer tab
  window.dispatchEvent(new CustomEvent('switch-tab', { 
    detail: { tab: 'zdog-viewer' } 
  }));
};

const openCADCAMViewer = (): void => {
  // Emit event to parent to switch to cadcam viewer tab
  window.dispatchEvent(new CustomEvent('switch-tab', { 
    detail: { tab: 'cadcam-viewer' } 
  }));
};

const loadInZdog = (): void => {
  if (!svgContent.value.trim()) return;
  
  // Store content and open zdog viewer
  localStorage.setItem('zdog-viewer-content', svgContent.value);
  openZdogViewer();
};

const loadInCADCAM = (): void => {
  if (!svgContent.value.trim()) return;
  
  // Store content and open cadcam viewer
  localStorage.setItem('cadcam-viewer-content', svgContent.value);
  openCADCAMViewer();
};

const useEditorContent = (): void => {
  if (!hasEditorContent.value) return;
  
  pendingContent.value = editorState.content;
  showViewerModal.value = true;
};

const confirmZdogViewer = (): void => {
  localStorage.setItem('zdog-viewer-content', pendingContent.value);
  closeModal();
  openZdogViewer();
};

const confirmCADCAMViewer = (): void => {
  localStorage.setItem('cadcam-viewer-content', pendingContent.value);
  closeModal();
  openCADCAMViewer();
};

const closeModal = (): void => {
  showViewerModal.value = false;
  pendingContent.value = '';
};

// Sample SVG content for demonstration
const loadSampleSVG = (): void => {
  svgContent.value = `<svg width="200" height="200" viewBox="0 0 200 200">
  <circle cx="100" cy="100" r="50" fill="#ff6b6b" stroke="#c92a2a" stroke-width="2"/>
  <rect x="75" y="75" width="50" height="50" fill="#51cf66" stroke="#37b24d" stroke-width="2"/>
</svg>`;
};

// Load sample on mount for demo
loadSampleSVG();
</script>

<style scoped lang="scss">
.zdog-integration-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.integration-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
  
  .header-content {
    h2 {
      margin: 0 0 4px 0;
      font-size: 20px;
      font-weight: 600;
      color: #495057;
    }
    
    p {
      margin: 0;
      color: #6c757d;
      font-size: 14px;
    }
  }
}

.header-actions {
  display: flex;
  gap: 12px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  background: #ffffff;
  color: #495057;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
  }
  
  &.primary {
    background: #007bff;
    border-color: #007bff;
    color: white;
    
    &:hover {
      background: #0056b3;
      border-color: #0056b3;
    }
  }
  
  &.secondary {
    background: #6c757d;
    border-color: #6c757d;
    color: white;
    
    &:hover {
      background: #545b62;
      border-color: #545b62;
    }
  }
}

.integration-panel {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.panel-section {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  
  h3 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
  }
  
  p {
    margin: 0 0 16px 0;
    color: #6c757d;
    font-size: 14px;
  }
}

.svg-input-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.svg-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  resize: vertical;
  
  &:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }
}

.input-actions {
  display: flex;
  gap: 12px;
}

.load-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover:not(:disabled) {
    background: #0056b3;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &.cadcam {
    background: #6c757d;
    
    &:hover:not(:disabled) {
      background: #545b62;
    }
  }
}

.editor-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.editor-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background: #218838;
  }
}

.editor-info {
  .info-text {
    font-size: 13px;
    color: #6c757d;
    font-style: italic;
  }
}

.features-section {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  
  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
  }
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.feature-card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  background: #f8f9fa;
  
  .feature-icon {
    margin-bottom: 12px;
    color: #007bff;
  }
  
  h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
  }
  
  p {
    margin: 0 0 12px 0;
    color: #6c757d;
    font-size: 14px;
  }
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0 0 16px 0;
  
  li {
    padding: 4px 0;
    font-size: 13px;
    color: #6c757d;
    
    &:before {
      content: '✓';
      color: #28a745;
      font-weight: bold;
      margin-right: 8px;
    }
  }
}

.feature-button {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background: #0056b3;
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 90%;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
  }
}

.close-button {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  
  &:hover {
    background: #f8f9fa;
    color: #495057;
  }
}

.modal-body {
  padding: 20px;
  
  p {
    margin: 0 0 16px 0;
    color: #6c757d;
    font-size: 14px;
  }
}

.viewer-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.viewer-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  
  &:hover {
    background: #f8f9fa;
    border-color: #007bff;
  }
  
  span {
    font-weight: 500;
    color: #495057;
  }
  
  small {
    color: #6c757d;
    font-size: 12px;
    margin-top: 2px;
  }
}

@media (max-width: 768px) {
  .integration-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .input-actions {
    flex-direction: column;
  }
}
</style>
