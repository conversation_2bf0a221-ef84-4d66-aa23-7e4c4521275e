/**
 * Geometry utility functions for 3D calculations and transformations
 */

export interface Point2D {
  x: number;
  y: number;
}

export interface Point3D {
  x: number;
  y: number;
  z: number;
}

export interface Matrix3D {
  m11: number; m12: number; m13: number; m14: number;
  m21: number; m22: number; m23: number; m24: number;
  m31: number; m32: number; m33: number; m34: number;
  m41: number; m42: number; m43: number; m44: number;
}

export interface BoundingBox {
  min: Point3D;
  max: Point3D;
  center: Point3D;
  size: Point3D;
}

/**
 * Calculate distance between two 2D points
 */
export function distance2D(p1: Point2D, p2: Point2D): number {
  const dx = p2.x - p1.x;
  const dy = p2.y - p1.y;
  return Math.sqrt(dx * dx + dy * dy);
}

/**
 * Calculate distance between two 3D points
 */
export function distance3D(p1: Point3D, p2: Point3D): number {
  const dx = p2.x - p1.x;
  const dy = p2.y - p1.y;
  const dz = p2.z - p1.z;
  return Math.sqrt(dx * dx + dy * dy + dz * dz);
}

/**
 * Linear interpolation between two points
 */
export function lerp2D(p1: Point2D, p2: Point2D, t: number): Point2D {
  return {
    x: p1.x + (p2.x - p1.x) * t,
    y: p1.y + (p2.y - p1.y) * t
  };
}

/**
 * Linear interpolation between two 3D points
 */
export function lerp3D(p1: Point3D, p2: Point3D, t: number): Point3D {
  return {
    x: p1.x + (p2.x - p1.x) * t,
    y: p1.y + (p2.y - p1.y) * t,
    z: p1.z + (p2.z - p1.z) * t
  };
}

/**
 * Normalize a 3D vector
 */
export function normalize3D(point: Point3D): Point3D {
  const length = Math.sqrt(point.x * point.x + point.y * point.y + point.z * point.z);
  if (length === 0) return { x: 0, y: 0, z: 0 };
  
  return {
    x: point.x / length,
    y: point.y / length,
    z: point.z / length
  };
}

/**
 * Cross product of two 3D vectors
 */
export function cross3D(a: Point3D, b: Point3D): Point3D {
  return {
    x: a.y * b.z - a.z * b.y,
    y: a.z * b.x - a.x * b.z,
    z: a.x * b.y - a.y * b.x
  };
}

/**
 * Dot product of two 3D vectors
 */
export function dot3D(a: Point3D, b: Point3D): number {
  return a.x * b.x + a.y * b.y + a.z * b.z;
}

/**
 * Calculate bounding box for a set of 3D points
 */
export function calculateBoundingBox(points: Point3D[]): BoundingBox {
  if (points.length === 0) {
    const origin = { x: 0, y: 0, z: 0 };
    return {
      min: origin,
      max: origin,
      center: origin,
      size: origin
    };
  }

  let minX = points[0].x, maxX = points[0].x;
  let minY = points[0].y, maxY = points[0].y;
  let minZ = points[0].z, maxZ = points[0].z;

  for (const point of points) {
    minX = Math.min(minX, point.x);
    maxX = Math.max(maxX, point.x);
    minY = Math.min(minY, point.y);
    maxY = Math.max(maxY, point.y);
    minZ = Math.min(minZ, point.z);
    maxZ = Math.max(maxZ, point.z);
  }

  const min = { x: minX, y: minY, z: minZ };
  const max = { x: maxX, y: maxY, z: maxZ };
  const center = {
    x: (minX + maxX) / 2,
    y: (minY + maxY) / 2,
    z: (minZ + maxZ) / 2
  };
  const size = {
    x: maxX - minX,
    y: maxY - minY,
    z: maxZ - minZ
  };

  return { min, max, center, size };
}

/**
 * Convert degrees to radians
 */
export function degToRad(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Convert radians to degrees
 */
export function radToDeg(radians: number): number {
  return radians * (180 / Math.PI);
}

/**
 * Clamp a value between min and max
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max);
}

/**
 * Create a 3D rotation matrix around X axis
 */
export function rotationMatrixX(angle: number): Matrix3D {
  const cos = Math.cos(angle);
  const sin = Math.sin(angle);
  
  return {
    m11: 1, m12: 0,   m13: 0,    m14: 0,
    m21: 0, m22: cos, m23: -sin, m24: 0,
    m31: 0, m32: sin, m33: cos,  m34: 0,
    m41: 0, m42: 0,   m43: 0,    m44: 1
  };
}

/**
 * Create a 3D rotation matrix around Y axis
 */
export function rotationMatrixY(angle: number): Matrix3D {
  const cos = Math.cos(angle);
  const sin = Math.sin(angle);
  
  return {
    m11: cos,  m12: 0, m13: sin, m14: 0,
    m21: 0,    m22: 1, m23: 0,   m24: 0,
    m31: -sin, m32: 0, m33: cos, m34: 0,
    m41: 0,    m42: 0, m43: 0,   m44: 1
  };
}

/**
 * Create a 3D rotation matrix around Z axis
 */
export function rotationMatrixZ(angle: number): Matrix3D {
  const cos = Math.cos(angle);
  const sin = Math.sin(angle);
  
  return {
    m11: cos, m12: -sin, m13: 0, m14: 0,
    m21: sin, m22: cos,  m23: 0, m24: 0,
    m31: 0,   m32: 0,    m33: 1, m34: 0,
    m41: 0,   m42: 0,    m43: 0, m44: 1
  };
}

/**
 * Apply matrix transformation to a 3D point
 */
export function transformPoint3D(point: Point3D, matrix: Matrix3D): Point3D {
  return {
    x: point.x * matrix.m11 + point.y * matrix.m12 + point.z * matrix.m13 + matrix.m14,
    y: point.x * matrix.m21 + point.y * matrix.m22 + point.z * matrix.m23 + matrix.m24,
    z: point.x * matrix.m31 + point.y * matrix.m32 + point.z * matrix.m33 + matrix.m34
  };
}

/**
 * Check if a point is inside a bounding box
 */
export function pointInBoundingBox(point: Point3D, box: BoundingBox): boolean {
  return point.x >= box.min.x && point.x <= box.max.x &&
         point.y >= box.min.y && point.y <= box.max.y &&
         point.z >= box.min.z && point.z <= box.max.z;
}
