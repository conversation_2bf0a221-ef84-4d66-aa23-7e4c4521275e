/**
 * TypeScript types for Zdog SVG Viewer configuration and state management
 */

import type { Illustration, Shape, Group } from 'zdog';
import type { ParsedSVGElement, SVGImportOptions } from './svg-types';

export interface ZdogViewerOptions {
  container: HTMLElement;
  width?: number;
  height?: number;
  backgroundColor?: string;
  pixelRatio?: number;
  dragRotate?: boolean;
  autoResize?: boolean;
  zoom?: number;
}

export interface ViewerControls {
  autoRotate: boolean;
  rotationSpeed: number;
  zoom: number;
  panX: number;
  panY: number;
  depth: number;
  strokeWidth: number;
  showWireframe: boolean;
  enableShadows: boolean;
}

export interface ViewerState {
  readonly shapes: ReadonlyMap<string, Shape>;
  readonly controls: Readonly<ViewerControls>;
  readonly isAnimating: boolean;
  readonly lastRenderTime: number;
  readonly fps: number;
  readonly isLoading: boolean;
  readonly error: Error | null;
}

export interface AnimationOptions {
  duration?: number;
  easing?: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out';
  loop?: boolean;
  autoStart?: boolean;
}

export interface ExportOptions {
  format: 'png' | 'svg' | 'webp';
  quality?: number;
  width?: number;
  height?: number;
  backgroundColor?: string;
  scale?: number;
}

export interface PerformanceMetrics {
  fps: number;
  renderTime: number;
  shapeCount: number;
  triangleCount: number;
  memoryUsage: number;
}

// Event system types
export type ViewerEvent = 
  | { type: 'load'; data: { shapeCount: number; loadTime: number } }
  | { type: 'error'; data: { error: Error; context?: string } }
  | { type: 'render'; data: { fps: number; renderTime: number } }
  | { type: 'export'; data: { format: string; size: number; blob: Blob } }
  | { type: 'interaction'; data: { type: 'drag' | 'zoom' | 'pan'; delta: { x: number; y: number } } }
  | { type: 'animation'; data: { state: 'start' | 'stop' | 'pause' | 'resume' } }
  | { type: 'shape-update'; data: { shapeId: string; properties: Partial<ShapeProperties> } };

export interface EventEmitter<T extends ViewerEvent> {
  on(event: T['type'], handler: (data: T['data']) => void): void;
  off(event: T['type'], handler: (data: T['data']) => void): void;
  emit(event: T): void;
  once(event: T['type'], handler: (data: T['data']) => void): void;
}

// Shape management types
export interface ShapeProperties {
  color: string;
  stroke: number | string;
  fill: boolean | string;
  visible: boolean;
  translate: { x: number; y: number; z: number };
  rotate: { x: number; y: number; z: number };
  scale: { x: number; y: number; z: number };
}

export interface ShapeUpdateOptions {
  animate?: boolean;
  duration?: number;
  easing?: string;
}

export interface ZdogShape {
  id: string;
  shape: Shape;
  originalElement: ParsedSVGElement;
  properties: ShapeProperties;
  bounds: { x: number; y: number; width: number; height: number };
}

// Plugin system types
export interface ZdogPlugin {
  name: string;
  version: string;
  install(viewer: ZdogSVGViewer): void;
  uninstall?(viewer: ZdogSVGViewer): void;
  dependencies?: string[];
}

export interface PluginManager {
  register(plugin: ZdogPlugin): void;
  unregister(pluginName: string): void;
  getPlugin(name: string): ZdogPlugin | undefined;
  listPlugins(): ZdogPlugin[];
}

// Interaction types
export interface InteractionState {
  isDragging: boolean;
  isPanning: boolean;
  isZooming: boolean;
  lastPointer: { x: number; y: number };
  dragStart: { x: number; y: number };
  rotationStart: { x: number; y: number; z: number };
}

export interface PointerEvent {
  x: number;
  y: number;
  deltaX: number;
  deltaY: number;
  button: number;
  ctrlKey: boolean;
  shiftKey: boolean;
  altKey: boolean;
}

// Rendering types
export interface RenderOptions {
  wireframe?: boolean;
  shadows?: boolean;
  lighting?: boolean;
  antialiasing?: boolean;
  backgroundColor?: string;
}

export interface Camera {
  position: { x: number; y: number; z: number };
  rotation: { x: number; y: number; z: number };
  zoom: number;
  fov?: number;
}

// Error types
export class ZdogViewerError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly context?: any
  ) {
    super(message);
    this.name = 'ZdogViewerError';
  }
}

export class ZdogRenderError extends ZdogViewerError {
  constructor(
    message: string,
    public readonly shapeId?: string,
    public readonly renderStage?: string
  ) {
    super(message, 'RENDER_ERROR', { shapeId, renderStage });
    this.name = 'ZdogRenderError';
  }
}

export class ZdogAnimationError extends ZdogViewerError {
  constructor(
    message: string,
    public readonly animationId?: string
  ) {
    super(message, 'ANIMATION_ERROR', { animationId });
    this.name = 'ZdogAnimationError';
  }
}

// Configuration types
export interface ViewerConfiguration {
  performance: {
    maxShapes: number;
    targetFPS: number;
    enableVirtualization: boolean;
    memoryLimit: number;
  };
  rendering: {
    antialiasing: boolean;
    shadows: boolean;
    wireframe: boolean;
    backgroundColor: string;
  };
  interaction: {
    dragRotate: boolean;
    zoomSensitivity: number;
    panSensitivity: number;
    enableKeyboard: boolean;
  };
  animation: {
    defaultDuration: number;
    defaultEasing: string;
    maxConcurrentAnimations: number;
  };
}

// Forward declaration for circular dependency
export interface ZdogSVGViewerInterface extends EventEmitter<ViewerEvent> {
  readonly state: ViewerState;
  readonly configuration: ViewerConfiguration;
  loadSVG(svgContent: string, options?: SVGImportOptions): Promise<void>;
  updateShape(id: string, properties: Partial<ShapeProperties>, options?: ShapeUpdateOptions): void;
  exportImage(options: ExportOptions): Promise<Blob>;
  destroy(): void;
}
