<template>
  <div class="editor-viewer-container">
    <!-- Split Pane Layout -->
    <div class="split-pane" :style="{ gridTemplateColumns: `${leftPaneWidth}px 4px 1fr` }">
      <!-- Left Pane: Lua Editor -->
      <div class="editor-pane">
        <div class="editor-header">
          <h3 class="editor-title">{{ $t('editor.title') }}</h3>
          <div class="editor-actions">
            <button
              @click="loadSVGFromEditor"
              :disabled="!editorContent.trim() || isLoading"
              class="action-button primary"
              :title="$t('zdog.loadSVG')"
            >
              <Play :size="16" />
              {{ $t('zdog.loadSVG') }}
            </button>
            
            <button
              @click="clearViewer"
              :disabled="!hasShapes"
              class="action-button secondary"
              :title="$t('zdog.clear')"
            >
              <Trash2 :size="16" />
              {{ $t('zdog.clear') }}
            </button>
          </div>
        </div>
        
        <div class="editor-content">
          <Editor
            ref="editorRef"
            :file-content="editorContent"
            :language="editorLanguage"
            @content-changed="onEditorContentChanged"
            @save="onEditorSave"
          />
        </div>
      </div>

      <!-- Splitter -->
      <div
        class="splitter"
        @mousedown="startResize"
        :class="{ resizing: isResizing }"
      ></div>

      <!-- Right Pane: Zdog Viewer -->
      <div class="viewer-pane">
        <div class="viewer-header">
          <h3 class="viewer-title">{{ $t('zdog.viewer.title') }}</h3>
          <div class="viewer-status">
            <span v-if="isLoading" class="status-loading">
              <Loader2 :size="14" class="animate-spin" />
              {{ $t('zdog.loading') }}
            </span>
            <span v-else-if="hasShapes" class="status-loaded">
              {{ shapeCount }} {{ $t('zdog.shapes') }}
            </span>
            <span v-else class="status-empty">
              {{ $t('zdog.noContent') }}
            </span>
          </div>
        </div>
        
        <div class="viewer-content">
          <!-- Zdog Canvas Container -->
          <div
            ref="zdogContainer"
            class="zdog-canvas-container"
            :class="{ loading: isLoading }"
          >
            <div v-if="!zdogViewer && !isLoading" class="empty-state">
              <FileText :size="48" />
              <h4>{{ $t('zdog.emptyState.title') }}</h4>
              <p>{{ $t('zdog.emptyState.description') }}</p>
            </div>
            
            <div v-if="isLoading" class="loading-overlay">
              <Loader2 :size="32" class="animate-spin" />
              <span>{{ $t('zdog.loading') }}</span>
            </div>
          </div>
          
          <!-- Error Display -->
          <div v-if="error" class="error-display">
            <AlertCircle :size="16" />
            <span>{{ error }}</span>
            <button @click="clearError" class="error-close">
              <X :size="14" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Controls Panel (Collapsible) -->
    <div class="controls-panel" :class="{ collapsed: controlsCollapsed }">
      <div class="controls-header">
        <h4>{{ $t('zdog.controls.title') }}</h4>
        <button
          @click="toggleControls"
          class="controls-toggle"
          :title="controlsCollapsed ? $t('zdog.controls.expand') : $t('zdog.controls.collapse')"
        >
          <ChevronUp v-if="!controlsCollapsed" :size="16" />
          <ChevronDown v-if="controlsCollapsed" :size="16" />
        </button>
      </div>
      
      <div v-if="!controlsCollapsed" class="controls-content">
        <ZdogControls
          v-if="zdogViewer"
          :viewer="zdogViewer"
          @export="onExport"
          @controls-change="onControlsChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { 
  Play, 
  Trash2, 
  Loader2, 
  FileText, 
  AlertCircle, 
  X, 
  ChevronUp, 
  ChevronDown 
} from 'lucide-vue-next';
import Editor from '@/components/Editor.vue';
import ZdogControls from '../viewers/zdog/ZdogControls.vue';
import { ZdogSVGViewer } from '../viewers/zdog/ZdogViewer';
import { ZdogExporter } from '../viewers/zdog/ZdogExporter';
import type { 
  ViewerControls, 
  ExportOptions, 
  ZdogViewerError 
} from '../viewers/zdog/types/viewer-types';
import type { SVGImportOptions } from '../viewers/zdog/types/svg-types';
import { useI18n } from '@/composables/useI18n';

interface Props {
  initialContent?: string;
  language?: 'svg' | 'xml' | 'html';
  autoLoad?: boolean;
  splitRatio?: number;
}

interface Emits {
  (e: 'content-changed', content: string): void;
  (e: 'svg-loaded', shapeCount: number): void;
  (e: 'export-complete', blob: Blob): void;
  (e: 'error', error: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  initialContent: '',
  language: 'svg',
  autoLoad: false,
  splitRatio: 0.5
});

const emit = defineEmits<Emits>();
const { t } = useI18n();

// Refs
const editorRef = ref<InstanceType<typeof Editor>>();
const zdogContainer = ref<HTMLElement>();

// State
const editorContent = ref(props.initialContent);
const editorLanguage = ref(props.language);
const zdogViewer = ref<ZdogSVGViewer | null>(null);
const zdogExporter = ref<ZdogExporter | null>(null);
const isLoading = ref(false);
const error = ref<string | null>(null);
const shapeCount = ref(0);
const controlsCollapsed = ref(false);

// Layout state
const leftPaneWidth = ref(600);
const isResizing = ref(false);
const resizeStartX = ref(0);
const resizeStartWidth = ref(0);

// Computed
const hasShapes = computed(() => shapeCount.value > 0);

// SVG Import options
const importOptions = reactive<SVGImportOptions>({
  depth: 20,
  strokeWidth: 2,
  preserveAspectRatio: true,
  scale: 1,
  centerOrigin: true,
  extrudeMode: 'uniform'
});

// Methods
const initializeZdogViewer = async (): Promise<void> => {
  if (!zdogContainer.value) {
    throw new Error('Zdog container not found');
  }

  try {
    // Create Zdog viewer
    zdogViewer.value = new ZdogSVGViewer({
      container: zdogContainer.value,
      width: zdogContainer.value.clientWidth,
      height: zdogContainer.value.clientHeight,
      backgroundColor: '#f8f9fa',
      dragRotate: true,
      autoResize: true
    });

    // Create exporter
    zdogExporter.value = new ZdogExporter(zdogViewer.value);

    // Setup event listeners
    setupZdogEventListeners();

    console.log('Zdog viewer initialized successfully');
  } catch (err) {
    const errorMessage = `Failed to initialize Zdog viewer: ${err}`;
    console.error(errorMessage);
    setError(errorMessage);
  }
};

const setupZdogEventListeners = (): void => {
  if (!zdogViewer.value) return;

  zdogViewer.value.on('load', (data) => {
    shapeCount.value = data.shapeCount;
    emit('svg-loaded', data.shapeCount);
    console.log(`Loaded ${data.shapeCount} shapes in ${data.loadTime.toFixed(2)}ms`);
  });

  zdogViewer.value.on('error', (data) => {
    setError(data.error.message);
    emit('error', data.error.message);
  });

  zdogViewer.value.on('render', (data) => {
    // Optional: Handle render events for performance monitoring
  });
};

const loadSVGFromEditor = async (): Promise<void> => {
  if (!zdogViewer.value || !editorContent.value.trim()) return;

  isLoading.value = true;
  clearError();

  try {
    await zdogViewer.value.loadSVG(editorContent.value, importOptions);
  } catch (err) {
    const errorMessage = err instanceof ZdogViewerError ? err.message : `Failed to load SVG: ${err}`;
    setError(errorMessage);
  } finally {
    isLoading.value = false;
  }
};

const clearViewer = (): void => {
  if (!zdogViewer.value) return;

  try {
    // Clear all shapes by loading empty SVG
    zdogViewer.value.loadSVG('<svg></svg>');
    shapeCount.value = 0;
    clearError();
  } catch (err) {
    setError(`Failed to clear viewer: ${err}`);
  }
};

const onEditorContentChanged = (content: string): void => {
  editorContent.value = content;
  emit('content-changed', content);

  // Auto-load if enabled and content looks like SVG
  if (props.autoLoad && content.trim().startsWith('<svg')) {
    loadSVGFromEditor();
  }
};

const onEditorSave = (): void => {
  // Optional: Handle save events
  console.log('Editor content saved');
};

const onExport = async (options: ExportOptions): Promise<void> => {
  if (!zdogExporter.value) return;

  try {
    const result = await zdogExporter.value.exportImage(options);
    emit('export-complete', result.blob);
    
    // Auto-download the exported file
    const filename = ZdogExporter.generateFilename(options.format, 'zdog-svg-export');
    ZdogExporter.downloadBlob(result.blob, filename);
  } catch (err) {
    setError(`Export failed: ${err}`);
  }
};

const onControlsChange = (controls: ViewerControls): void => {
  // Update import options based on control changes
  importOptions.depth = controls.depth;
  importOptions.strokeWidth = controls.strokeWidth;
};

const setError = (message: string): void => {
  error.value = message;
  console.error('EditorViewer error:', message);
};

const clearError = (): void => {
  error.value = null;
};

const toggleControls = (): void => {
  controlsCollapsed.value = !controlsCollapsed.value;
};

// Resize handling
const startResize = (event: MouseEvent): void => {
  isResizing.value = true;
  resizeStartX.value = event.clientX;
  resizeStartWidth.value = leftPaneWidth.value;
  
  document.addEventListener('mousemove', handleResize);
  document.addEventListener('mouseup', stopResize);
  event.preventDefault();
};

const handleResize = (event: MouseEvent): void => {
  if (!isResizing.value) return;
  
  const deltaX = event.clientX - resizeStartX.value;
  const newWidth = resizeStartWidth.value + deltaX;
  
  // Constrain width between 300px and 80% of container
  const container = document.querySelector('.editor-viewer-container') as HTMLElement;
  const maxWidth = container ? container.clientWidth * 0.8 : 1000;
  
  leftPaneWidth.value = Math.max(300, Math.min(newWidth, maxWidth));
};

const stopResize = (): void => {
  isResizing.value = false;
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);
};

// Lifecycle
onMounted(async () => {
  await nextTick();
  
  // Initialize split pane width
  const container = document.querySelector('.editor-viewer-container') as HTMLElement;
  if (container) {
    leftPaneWidth.value = container.clientWidth * props.splitRatio;
  }
  
  // Initialize Zdog viewer
  await initializeZdogViewer();
  
  // Auto-load if content is provided and auto-load is enabled
  if (props.autoLoad && props.initialContent.trim()) {
    await loadSVGFromEditor();
  }
});

onUnmounted(() => {
  // Cleanup
  if (zdogViewer.value) {
    zdogViewer.value.destroy();
  }
  
  // Remove resize event listeners
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);
});

// Watch for container resize
watch(() => zdogContainer.value, async (newContainer) => {
  if (newContainer && zdogViewer.value) {
    // Reinitialize viewer with new container
    await initializeZdogViewer();
  }
});
</script>

<style scoped lang="scss">
.editor-viewer-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8f9fa;
}

.split-pane {
  display: grid;
  flex: 1;
  min-height: 0;
  gap: 0;
}

.editor-pane,
.viewer-pane {
  display: flex;
  flex-direction: column;
  min-height: 0;
  background: #ffffff;
  border: 1px solid #e9ecef;
}

.editor-header,
.viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.editor-title,
.viewer-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin: 0;
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: #ffffff;
  color: #495057;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    background: #f8f9fa;
    border-color: #adb5bd;
  }
  
  &.primary {
    background: #007bff;
    border-color: #007bff;
    color: white;
    
    &:hover:not(:disabled) {
      background: #0056b3;
      border-color: #0056b3;
    }
  }
  
  &.secondary {
    background: #6c757d;
    border-color: #6c757d;
    color: white;
    
    &:hover:not(:disabled) {
      background: #545b62;
      border-color: #545b62;
    }
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.viewer-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.status-loading {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #007bff;
}

.status-loaded {
  color: #28a745;
  font-weight: 500;
}

.status-empty {
  color: #6c757d;
}

.editor-content {
  flex: 1;
  min-height: 0;
}

.viewer-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.zdog-canvas-container {
  flex: 1;
  position: relative;
  min-height: 0;
  
  &.loading {
    pointer-events: none;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6c757d;
  text-align: center;
  padding: 32px;
  
  h4 {
    margin: 16px 0 8px 0;
    font-size: 16px;
    font-weight: 600;
  }
  
  p {
    margin: 0;
    font-size: 14px;
    opacity: 0.8;
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  color: #007bff;
  font-size: 14px;
  font-weight: 500;
  gap: 12px;
}

.error-display {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  color: #721c24;
  font-size: 12px;
  margin: 8px;
}

.error-close {
  margin-left: auto;
  background: none;
  border: none;
  color: #721c24;
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
  
  &:hover {
    background: rgba(114, 28, 36, 0.1);
  }
}

.splitter {
  background: #e9ecef;
  cursor: col-resize;
  transition: background-color 0.2s ease;
  
  &:hover,
  &.resizing {
    background: #007bff;
  }
}

.controls-panel {
  background: #ffffff;
  border-top: 1px solid #e9ecef;
  transition: all 0.3s ease;
  
  &.collapsed {
    .controls-content {
      display: none;
    }
  }
}

.controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  
  h4 {
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    margin: 0;
  }
}

.controls-toggle {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  
  &:hover {
    background: #e9ecef;
    color: #495057;
  }
}

.controls-content {
  max-height: 300px;
  overflow-y: auto;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
