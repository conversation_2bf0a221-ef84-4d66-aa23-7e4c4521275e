<template>
  <div class="zdog-viewer-example">
    <h2>Zdog 3D SVG Viewer Integration Example</h2>
    
    <!-- Quick Start Example -->
    <div class="example-section">
      <h3>Quick Start</h3>
      <p>Basic usage of the Zdog SVG viewer with a simple SVG:</p>
      
      <div class="example-demo">
        <div ref="basicViewerContainer" class="viewer-container"></div>
        <div class="example-code">
          <pre><code>{{ basicExample }}</code></pre>
        </div>
      </div>
      
      <button @click="loadBasicExample" class="demo-button">
        Load Basic Example
      </button>
    </div>

    <!-- Advanced Example with Controls -->
    <div class="example-section">
      <h3>Advanced Example with Controls</h3>
      <p>Full-featured viewer with interactive controls:</p>
      
      <EditorViewer
        :initial-content="advancedSVG"
        language="svg"
        :auto-load="true"
        :split-ratio="0.6"
        @svg-loaded="onAdvancedLoaded"
        @export-complete="onExportComplete"
        @error="onError"
      />
    </div>

    <!-- Performance Example -->
    <div class="example-section">
      <h3>Performance Optimized Example</h3>
      <p>Large SVG with performance optimizations:</p>
      
      <div class="performance-controls">
        <label>
          <input type="checkbox" v-model="enableWorker" />
          Enable Web Worker
        </label>
        <label>
          <input type="checkbox" v-model="enableCulling" />
          Enable Viewport Culling
        </label>
        <label>
          <input type="checkbox" v-model="enableLOD" />
          Enable Level of Detail
        </label>
      </div>
      
      <div ref="performanceViewerContainer" class="viewer-container large"></div>
      
      <button @click="loadPerformanceExample" class="demo-button">
        Load Performance Example (1000+ shapes)
      </button>
    </div>

    <!-- Export Example -->
    <div class="example-section">
      <h3>Export Functionality</h3>
      <p>Export the 3D view as various image formats:</p>
      
      <div class="export-controls">
        <select v-model="exportFormat">
          <option value="png">PNG</option>
          <option value="webp">WebP</option>
          <option value="svg">SVG</option>
        </select>
        
        <input 
          type="range" 
          min="0.1" 
          max="1" 
          step="0.1" 
          v-model="exportQuality"
        />
        <span>Quality: {{ (exportQuality * 100).toFixed(0) }}%</span>
        
        <button @click="exportCurrentView" class="demo-button">
          Export Image
        </button>
      </div>
    </div>

    <!-- Status Display -->
    <div class="status-section">
      <h3>Status & Metrics</h3>
      <div class="status-grid">
        <div class="status-item">
          <span class="label">Shapes Loaded:</span>
          <span class="value">{{ totalShapes }}</span>
        </div>
        <div class="status-item">
          <span class="label">Last Export:</span>
          <span class="value">{{ lastExportInfo }}</span>
        </div>
        <div class="status-item">
          <span class="label">Errors:</span>
          <span class="value error">{{ errorCount }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { ZdogSVGViewer, createViewer, testSVGs } from '@/viewers/zdog';
import EditorViewer from '@/integration/EditorViewer.vue';
import type { ExportOptions } from '@/viewers/zdog/types/viewer-types';

// Refs
const basicViewerContainer = ref<HTMLElement>();
const performanceViewerContainer = ref<HTMLElement>();

// State
const totalShapes = ref(0);
const lastExportInfo = ref('None');
const errorCount = ref(0);
const enableWorker = ref(true);
const enableCulling = ref(true);
const enableLOD = ref(true);
const exportFormat = ref<'png' | 'webp' | 'svg'>('png');
const exportQuality = ref(0.9);

// Viewers
let basicViewer: ZdogSVGViewer | null = null;
let performanceViewer: ZdogSVGViewer | null = null;

// Example SVG content
const basicExample = `import { createViewer } from '@/viewers/zdog';

const viewer = createViewer(container, {
  width: 400,
  height: 300,
  backgroundColor: '#f8f9fa',
  dragRotate: true
});

await viewer.loadSVG(\`
  <svg viewBox="0 0 100 100">
    <circle cx="50" cy="50" r="25" fill="#ff6b6b" />
    <rect x="25" y="25" width="50" height="50" fill="#51cf66" />
  </svg>
\`, {
  depth: 15,
  centerOrigin: true
});`;

const advancedSVG = ref(testSVGs.logoDesign);

// Methods
const loadBasicExample = async (): Promise<void> => {
  if (!basicViewerContainer.value) return;

  try {
    if (basicViewer) {
      basicViewer.destroy();
    }

    basicViewer = createViewer(basicViewerContainer.value, {
      width: 400,
      height: 300,
      backgroundColor: '#f8f9fa',
      dragRotate: true
    });

    await basicViewer.loadSVG(`
      <svg viewBox="0 0 100 100">
        <circle cx="50" cy="50" r="25" fill="#ff6b6b" stroke="#c92a2a" stroke-width="2"/>
        <rect x="25" y="25" width="50" height="50" fill="#51cf66" stroke="#37b24d" stroke-width="2"/>
      </svg>
    `, {
      depth: 15,
      centerOrigin: true
    });

    totalShapes.value += basicViewer.state.shapes.size;

  } catch (error) {
    console.error('Failed to load basic example:', error);
    errorCount.value++;
  }
};

const loadPerformanceExample = async (): Promise<void> => {
  if (!performanceViewerContainer.value) return;

  try {
    if (performanceViewer) {
      performanceViewer.destroy();
    }

    performanceViewer = createViewer(performanceViewerContainer.value, {
      width: 600,
      height: 400,
      backgroundColor: '#ffffff',
      dragRotate: true
    });

    // Configure performance settings
    performanceViewer.configuration.performance = {
      maxShapes: enableWorker.value ? 5000 : 1000,
      targetFPS: 60,
      enableVirtualization: enableCulling.value,
      memoryLimit: 100 * 1024 * 1024
    };

    // Generate a complex SVG with many shapes
    const complexSVG = generateComplexSVG(1000);
    
    await performanceViewer.loadSVG(complexSVG, {
      depth: 10,
      strokeWidth: 1,
      centerOrigin: true
    });

    totalShapes.value += performanceViewer.state.shapes.size;

  } catch (error) {
    console.error('Failed to load performance example:', error);
    errorCount.value++;
  }
};

const generateComplexSVG = (shapeCount: number): string => {
  let svg = '<svg width="800" height="600" viewBox="0 0 800 600">';
  
  for (let i = 0; i < shapeCount; i++) {
    const x = Math.random() * 800;
    const y = Math.random() * 600;
    const size = Math.random() * 20 + 5;
    const color = `hsl(${Math.random() * 360}, 70%, 50%)`;
    
    if (i % 3 === 0) {
      svg += `<circle cx="${x}" cy="${y}" r="${size}" fill="${color}" opacity="0.7"/>`;
    } else if (i % 3 === 1) {
      svg += `<rect x="${x}" y="${y}" width="${size}" height="${size}" fill="${color}" opacity="0.7"/>`;
    } else {
      const points = `${x},${y} ${x+size},${y+size/2} ${x},${y+size}`;
      svg += `<polygon points="${points}" fill="${color}" opacity="0.7"/>`;
    }
  }
  
  svg += '</svg>';
  return svg;
};

const exportCurrentView = async (): Promise<void> => {
  const viewer = performanceViewer || basicViewer;
  if (!viewer) return;

  try {
    const options: ExportOptions = {
      format: exportFormat.value,
      quality: exportQuality.value,
      scale: 2
    };

    const blob = await viewer.exportImage(options);
    
    // Download the file
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `zdog-export.${exportFormat.value}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    lastExportInfo.value = `${exportFormat.value.toUpperCase()} (${(blob.size / 1024).toFixed(1)} KB)`;

  } catch (error) {
    console.error('Export failed:', error);
    errorCount.value++;
  }
};

// Event handlers
const onAdvancedLoaded = (shapeCount: number): void => {
  totalShapes.value += shapeCount;
};

const onExportComplete = (blob: Blob): void => {
  lastExportInfo.value = `Export (${(blob.size / 1024).toFixed(1)} KB)`;
};

const onError = (error: string): void => {
  console.error('Viewer error:', error);
  errorCount.value++;
};

// Lifecycle
onMounted(() => {
  // Load basic example on mount
  setTimeout(loadBasicExample, 100);
});

onUnmounted(() => {
  if (basicViewer) {
    basicViewer.destroy();
  }
  if (performanceViewer) {
    performanceViewer.destroy();
  }
});
</script>

<style scoped lang="scss">
.zdog-viewer-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.example-section {
  margin-bottom: 40px;
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  h3 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 18px;
  }
  
  p {
    margin: 0 0 16px 0;
    color: #6c757d;
    font-size: 14px;
  }
}

.example-demo {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 16px;
}

.viewer-container {
  border: 1px solid #e9ecef;
  border-radius: 4px;
  background: #f8f9fa;
  
  &.large {
    height: 400px;
  }
}

.example-code {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  
  pre {
    margin: 0;
    font-size: 12px;
    line-height: 1.4;
    overflow-x: auto;
    
    code {
      color: #495057;
    }
  }
}

.demo-button {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
  
  &:hover {
    background: #0056b3;
  }
}

.performance-controls,
.export-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  
  label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: #495057;
  }
  
  select,
  input[type="range"] {
    padding: 4px 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
  }
}

.status-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  
  h3 {
    margin: 0 0 16px 0;
    color: #495057;
  }
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  
  .label {
    font-weight: 500;
    color: #6c757d;
  }
  
  .value {
    color: #495057;
    
    &.error {
      color: #dc3545;
    }
  }
}

@media (max-width: 768px) {
  .example-demo {
    grid-template-columns: 1fr;
  }
  
  .performance-controls,
  .export-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
}
</style>
