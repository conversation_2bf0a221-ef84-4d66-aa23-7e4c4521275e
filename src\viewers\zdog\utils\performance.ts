/**
 * Performance optimization utilities for Zdog SVG Viewer
 */

import type { ParsedSVGElement } from '../types/svg-types';
import type { PerformanceMetrics, ZdogShape } from '../types/viewer-types';

export interface PerformanceConfig {
  maxShapes: number;
  targetFPS: number;
  enableVirtualization: boolean;
  memoryLimit: number; // in bytes
  enableLOD: boolean; // Level of Detail
  cullingEnabled: boolean;
}

export interface ViewportInfo {
  x: number;
  y: number;
  width: number;
  height: number;
  zoom: number;
}

export class PerformanceOptimizer {
  private config: PerformanceConfig;
  private frameTimeHistory: number[] = [];
  private memoryUsageHistory: number[] = [];
  private lastFrameTime = 0;
  private frameCount = 0;

  constructor(config: PerformanceConfig) {
    this.config = config;
  }

  /**
   * Optimize SVG elements based on performance constraints
   */
  public optimizeElements(elements: ParsedSVGElement[]): ParsedSVGElement[] {
    let optimized = [...elements];

    // Apply various optimization strategies
    optimized = this.removeInvisibleElements(optimized);
    optimized = this.simplifyComplexPaths(optimized);
    optimized = this.mergeCompatibleElements(optimized);
    optimized = this.applyLevelOfDetail(optimized);

    // If still too many shapes, apply more aggressive optimizations
    if (optimized.length > this.config.maxShapes) {
      optimized = this.aggressiveOptimization(optimized);
    }

    return optimized;
  }

  /**
   * Perform viewport culling to hide shapes outside the view
   */
  public cullShapes(shapes: Map<string, ZdogShape>, viewport: ViewportInfo): string[] {
    if (!this.config.cullingEnabled) {
      return Array.from(shapes.keys());
    }

    const visibleShapeIds: string[] = [];

    for (const [id, zdogShape] of shapes) {
      if (this.isShapeInViewport(zdogShape, viewport)) {
        visibleShapeIds.push(id);
      }
    }

    return visibleShapeIds;
  }

  /**
   * Calculate current performance metrics
   */
  public calculateMetrics(renderTime: number): PerformanceMetrics {
    const currentTime = performance.now();
    
    // Calculate FPS
    this.frameCount++;
    const deltaTime = currentTime - this.lastFrameTime;
    
    if (deltaTime >= 1000) {
      const fps = Math.round((this.frameCount * 1000) / deltaTime);
      this.frameTimeHistory.push(fps);
      
      // Keep only last 60 samples
      if (this.frameTimeHistory.length > 60) {
        this.frameTimeHistory.shift();
      }
      
      this.frameCount = 0;
      this.lastFrameTime = currentTime;
    }

    // Calculate memory usage
    const memoryUsage = this.getMemoryUsage();
    this.memoryUsageHistory.push(memoryUsage);
    
    if (this.memoryUsageHistory.length > 60) {
      this.memoryUsageHistory.shift();
    }

    const avgFPS = this.frameTimeHistory.length > 0 
      ? Math.round(this.frameTimeHistory.reduce((a, b) => a + b, 0) / this.frameTimeHistory.length)
      : 0;

    return {
      fps: avgFPS,
      renderTime,
      shapeCount: 0, // Will be set by caller
      triangleCount: 0, // Estimated based on shapes
      memoryUsage
    };
  }

  /**
   * Check if performance optimization is needed
   */
  public needsOptimization(metrics: PerformanceMetrics): boolean {
    return metrics.fps < this.config.targetFPS || 
           metrics.memoryUsage > this.config.memoryLimit ||
           metrics.renderTime > 16.67; // 60 FPS threshold
  }

  /**
   * Get optimization suggestions based on current performance
   */
  public getOptimizationSuggestions(metrics: PerformanceMetrics): string[] {
    const suggestions: string[] = [];

    if (metrics.fps < this.config.targetFPS) {
      suggestions.push('Consider reducing the number of shapes or enabling Level of Detail');
    }

    if (metrics.renderTime > 16.67) {
      suggestions.push('Enable viewport culling to improve render performance');
    }

    if (metrics.memoryUsage > this.config.memoryLimit * 0.8) {
      suggestions.push('Memory usage is high, consider simplifying complex paths');
    }

    if (metrics.shapeCount > this.config.maxShapes) {
      suggestions.push('Too many shapes, enable shape merging or virtualization');
    }

    return suggestions;
  }

  /**
   * Remove elements that are not visible
   */
  private removeInvisibleElements(elements: ParsedSVGElement[]): ParsedSVGElement[] {
    return elements.filter(element => {
      const style = element.style;
      
      // Remove completely transparent elements
      if (style.opacity === 0) return false;
      
      // Remove elements with no fill and no stroke
      if (style.fill === 'none' && (!style.stroke || style.stroke === 'none')) return false;
      
      // Remove zero-sized elements
      if (element.bounds && (element.bounds.width === 0 || element.bounds.height === 0)) return false;
      
      return true;
    });
  }

  /**
   * Simplify complex paths to reduce rendering cost
   */
  private simplifyComplexPaths(elements: ParsedSVGElement[]): ParsedSVGElement[] {
    return elements.map(element => {
      if (element.type === 'path' && element.pathData) {
        // Simplify path if it's too complex
        const commandCount = (element.pathData.match(/[MmLlHhVvCcSsQqTtAaZz]/g) || []).length;
        
        if (commandCount > 100) {
          element.pathData = this.simplifyPathData(element.pathData);
        }
      }
      
      return element;
    });
  }

  /**
   * Merge compatible elements to reduce shape count
   */
  private mergeCompatibleElements(elements: ParsedSVGElement[]): ParsedSVGElement[] {
    const merged: ParsedSVGElement[] = [];
    const groups: Map<string, ParsedSVGElement[]> = new Map();

    // Group elements by type and style
    for (const element of elements) {
      const key = this.getElementGroupKey(element);
      
      if (!groups.has(key)) {
        groups.set(key, []);
      }
      
      groups.get(key)!.push(element);
    }

    // Merge groups where possible
    for (const [key, group] of groups) {
      if (group.length === 1) {
        merged.push(group[0]);
      } else {
        // Try to merge elements in the group
        const mergedElement = this.tryMergeElements(group);
        merged.push(...mergedElement);
      }
    }

    return merged;
  }

  /**
   * Apply Level of Detail optimization
   */
  private applyLevelOfDetail(elements: ParsedSVGElement[]): ParsedSVGElement[] {
    if (!this.config.enableLOD) return elements;

    return elements.map(element => {
      // Simplify small elements
      if (element.bounds && element.bounds.width < 5 && element.bounds.height < 5) {
        // Convert complex shapes to simple rectangles for small elements
        if (element.type === 'path' || element.type === 'polygon') {
          return {
            ...element,
            type: 'rect' as const,
            pathData: undefined
          };
        }
      }

      return element;
    });
  }

  /**
   * Apply aggressive optimization when performance is critical
   */
  private aggressiveOptimization(elements: ParsedSVGElement[]): ParsedSVGElement[] {
    // Sort by importance (size, visibility, etc.)
    const sorted = elements.sort((a, b) => {
      const aSize = (a.bounds?.width || 0) * (a.bounds?.height || 0);
      const bSize = (b.bounds?.width || 0) * (b.bounds?.height || 0);
      return bSize - aSize; // Larger elements first
    });

    // Keep only the most important shapes
    return sorted.slice(0, this.config.maxShapes);
  }

  /**
   * Check if a shape is within the viewport
   */
  private isShapeInViewport(shape: ZdogShape, viewport: ViewportInfo): boolean {
    const bounds = shape.bounds;
    
    // Simple bounding box intersection test
    return !(bounds.x + bounds.width < viewport.x ||
             bounds.x > viewport.x + viewport.width ||
             bounds.y + bounds.height < viewport.y ||
             bounds.y > viewport.y + viewport.height);
  }

  /**
   * Get current memory usage
   */
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return memory.usedJSHeapSize;
    }
    
    // Fallback estimation
    return 0;
  }

  /**
   * Simplify path data string
   */
  private simplifyPathData(pathData: string): string {
    return pathData
      .replace(/(\d+\.\d{2})\d+/g, '$1') // Reduce decimal precision
      .replace(/\s+/g, ' ') // Normalize whitespace
      .replace(/([MmLlHhVvCcSsQqTtAaZz])\s+/g, '$1') // Remove space after commands
      .trim();
  }

  /**
   * Get grouping key for element merging
   */
  private getElementGroupKey(element: ParsedSVGElement): string {
    const style = element.style;
    return `${element.type}-${style.fill}-${style.stroke}-${style.strokeWidth}`;
  }

  /**
   * Try to merge a group of compatible elements
   */
  private tryMergeElements(elements: ParsedSVGElement[]): ParsedSVGElement[] {
    // For now, just return the original elements
    // In a real implementation, you might merge paths or create groups
    return elements;
  }
}

/**
 * Memory pool for reusing objects to reduce garbage collection
 */
export class ObjectPool<T> {
  private pool: T[] = [];
  private createFn: () => T;
  private resetFn: (obj: T) => void;

  constructor(createFn: () => T, resetFn: (obj: T) => void, initialSize = 10) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    
    // Pre-populate pool
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(createFn());
    }
  }

  public acquire(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    
    return this.createFn();
  }

  public release(obj: T): void {
    this.resetFn(obj);
    this.pool.push(obj);
  }

  public clear(): void {
    this.pool.length = 0;
  }
}

/**
 * Debounce function for performance-sensitive operations
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: number | undefined;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = window.setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function for limiting function calls
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}
