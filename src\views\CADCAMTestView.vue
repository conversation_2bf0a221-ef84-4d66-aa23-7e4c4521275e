<template>
  <div class="cadcam-test-view">
    <div class="test-header">
      <h1>CAD/CAM Door Manufacturing Viewer</h1>
      <p>Test the Zdog viewer with door panel operations (top/bottom faces)</p>
    </div>

    <div class="main-content">
      <!-- Left Panel: SVG Editor -->
      <div class="editor-panel">
        <div class="panel-header">
          <h3>Door Panel SVG</h3>
          <div class="header-actions">
            <select v-model="selectedExample" @change="loadExample" class="example-select">
              <option value="">Select example...</option>
              <option value="simple-door">Simple Door Panel</option>
              <option value="complex-door">Complex Door with Operations</option>
              <option value="cabinet-door">Cabinet Door</option>
            </select>
            
            <button @click="loadCADCAM" class="load-button" :disabled="!svgContent.trim()">
              <Play :size="16" />
              Load CAD/CAM
            </button>
          </div>
        </div>
        
        <div class="editor-container">
          <textarea
            v-model="svgContent"
            class="svg-editor"
            placeholder="Enter SVG content with layer information..."
          ></textarea>
        </div>
      </div>

      <!-- Center Panel: 3D Viewer -->
      <div class="viewer-panel">
        <div class="panel-header">
          <h3>3D Visualization</h3>
          <div class="viewer-status">
            <span v-if="isLoading" class="status loading">
              <Loader2 :size="14" class="animate-spin" />
              Loading...
            </span>
            <span v-else-if="doorPanel" class="status loaded">
              {{ totalShapes }} shapes loaded
            </span>
            <span v-else class="status empty">
              No content loaded
            </span>
          </div>
        </div>
        
        <div ref="viewerContainer" class="viewer-container">
          <div v-if="!zdogViewer && !isLoading" class="empty-state">
            <Package :size="48" />
            <h4>No Door Panel Loaded</h4>
            <p>Load a CAD/CAM SVG to see the 3D visualization</p>
          </div>
        </div>
      </div>

      <!-- Right Panel: CAD/CAM Controls -->
      <div class="controls-panel">
        <CADCAMControls
          v-if="zdogViewer"
          :viewer="zdogViewer"
          @face-changed="onFaceChanged"
          @layer-toggled="onLayerToggled"
          @settings-changed="onSettingsChanged"
        />
        
        <div v-else class="no-controls">
          <Settings :size="32" />
          <p>Load a door panel to access controls</p>
        </div>
      </div>
    </div>

    <!-- Bottom Panel: Information -->
    <div class="info-panel">
      <div class="info-section">
        <h4>Door Panel Information</h4>
        <div v-if="doorPanel" class="door-info">
          <div class="info-grid">
            <div class="info-item">
              <span class="label">Dimensions:</span>
              <span class="value">{{ doorPanel.width }} × {{ doorPanel.height }}mm</span>
            </div>
            <div class="info-item">
              <span class="label">Thickness:</span>
              <span class="value">{{ doorPanel.thickness }}mm</span>
            </div>
            <div class="info-item">
              <span class="label">Top Operations:</span>
              <span class="value">{{ doorPanel.topLayers.length }}</span>
            </div>
            <div class="info-item">
              <span class="label">Bottom Operations:</span>
              <span class="value">{{ doorPanel.bottomLayers.length }}</span>
            </div>
          </div>
        </div>
        <div v-else class="no-info">
          No door panel loaded
        </div>
      </div>

      <div class="info-section">
        <h4>Layer Operations</h4>
        <div v-if="layers.length > 0" class="layer-summary">
          <div
            v-for="layer in layers"
            :key="layer.name"
            class="layer-summary-item"
            :class="`operation-${layer.operationType}`"
          >
            <span class="layer-name">{{ layer.name }}</span>
            <span class="layer-type">{{ formatOperationType(layer.operationType) }}</span>
            <span class="layer-face">{{ layer.face.toUpperCase() }}</span>
            <span v-if="layer.toolInfo" class="layer-tool">
              {{ layer.toolInfo.type }} Ø{{ layer.toolInfo.diameter }}mm
            </span>
          </div>
        </div>
        <div v-else class="no-layers">
          No layers detected
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { Play, Loader2, Package, Settings } from 'lucide-vue-next';
import { createViewer, loadCADCAMSVG } from '@/viewers/zdog';
import CADCAMControls from '@/viewers/zdog/CADCAMControls.vue';
import type { ZdogSVGViewer } from '@/viewers/zdog/ZdogViewer';
import type { DoorPanel, CADCAMLayer } from '@/viewers/zdog/CADCAMParser';

// Refs
const viewerContainer = ref<HTMLElement>();
const zdogViewer = ref<ZdogSVGViewer | null>(null);

// State
const svgContent = ref('');
const selectedExample = ref('');
const isLoading = ref(false);
const doorPanel = ref<DoorPanel | null>(null);
const layers = ref<CADCAMLayer[]>([]);
const totalShapes = ref(0);

// Example SVG content with CAD/CAM layers
const examples = {
  'simple-door': `
<svg width="600" height="800" viewBox="0 0 600 800" xmlns="http://www.w3.org/2000/svg">
  <!-- Panel outline -->
  <rect id="panel" data-layer="PANEL" x="50" y="50" width="500" height="700" 
        fill="none" stroke="#8B4513" stroke-width="2"/>
  
  <!-- Top face groove operations -->
  <rect data-layer="20MM" data-face="top" x="100" y="100" width="400" height="50" 
        fill="none" stroke="#ff6b6b" stroke-width="1"/>
  <rect data-layer="20MM" data-face="top" x="100" y="650" width="400" height="50" 
        fill="none" stroke="#ff6b6b" stroke-width="1"/>
  
  <!-- Center panel groove -->
  <rect data-layer="15MM" data-face="top" x="150" y="200" width="300" height="400" 
        fill="none" stroke="#51cf66" stroke-width="1"/>
  
  <!-- Bottom face drill operations -->
  <circle data-layer="DRILL8MM" data-face="bottom" cx="200" cy="150" r="4" 
          fill="none" stroke="#339af0" stroke-width="1"/>
  <circle data-layer="DRILL8MM" data-face="bottom" cx="400" cy="150" r="4" 
          fill="none" stroke="#339af0" stroke-width="1"/>
</svg>`,

  'complex-door': `
<svg width="600" height="800" viewBox="0 0 600 800" xmlns="http://www.w3.org/2000/svg">
  <!-- Panel -->
  <rect data-layer="PANEL" x="0" y="0" width="600" height="800" 
        fill="#d4a574" stroke="#8B4513" stroke-width="2"/>
  
  <!-- Top face operations -->
  <g data-face="top">
    <!-- Main groove pattern -->
    <rect data-layer="K25MM" x="50" y="50" width="500" height="60" 
          fill="none" stroke="#ff6b6b" stroke-width="2"/>
    <rect data-layer="K25MM" x="50" y="690" width="500" height="60" 
          fill="none" stroke="#ff6b6b" stroke-width="2"/>
    <rect data-layer="K25MM" x="50" y="50" width="60" height="700" 
          fill="none" stroke="#ff6b6b" stroke-width="2"/>
    <rect data-layer="K25MM" x="490" y="50" width="60" height="700" 
          fill="none" stroke="#ff6b6b" stroke-width="2"/>
    
    <!-- Center panel pocket -->
    <rect data-layer="POCKET15MM" x="150" y="150" width="300" height="500" 
          fill="none" stroke="#51cf66" stroke-width="2"/>
    
    <!-- Decorative V-carve -->
    <path data-layer="V90DECORATIVE" 
          d="M200,300 Q300,250 400,300 Q300,350 200,300" 
          fill="none" stroke="#ffd43b" stroke-width="1"/>
  </g>
  
  <!-- Bottom face operations -->
  <g data-face="bottom">
    <!-- Hinge holes -->
    <circle data-layer="DRILL35MM" cx="25" cy="200" r="17.5" 
            fill="none" stroke="#339af0" stroke-width="2"/>
    <circle data-layer="DRILL35MM" cx="25" cy="400" r="17.5" 
            fill="none" stroke="#339af0" stroke-width="2"/>
    <circle data-layer="DRILL35MM" cx="25" cy="600" r="17.5" 
            fill="none" stroke="#339af0" stroke-width="2"/>
    
    <!-- Handle preparation -->
    <rect data-layer="POCKET8MM" x="500" y="350" width="80" height="100" 
          fill="none" stroke="#51cf66" stroke-width="1"/>
  </g>
</svg>`,

  'cabinet-door': `
<svg width="400" height="600" viewBox="0 0 400 600" xmlns="http://www.w3.org/2000/svg">
  <!-- Cabinet door panel -->
  <rect data-layer="PANEL" x="0" y="0" width="400" height="600" 
        fill="#d4a574" stroke="#8B4513" stroke-width="2"/>
  
  <!-- Top face - raised panel effect -->
  <g data-face="top">
    <!-- Outer chamfer -->
    <rect data-layer="CHAMFER2MM" x="20" y="20" width="360" height="560" 
          fill="none" stroke="#ff8cc8" stroke-width="1"/>
    
    <!-- Inner raised area -->
    <rect data-layer="POCKET3MM" x="40" y="40" width="320" height="520" 
          fill="none" stroke="#51cf66" stroke-width="1"/>
  </g>
  
  <!-- Bottom face - hardware -->
  <g data-face="bottom">
    <!-- Hinge cups -->
    <circle data-layer="DRILL35MM" cx="50" cy="100" r="17.5" 
            fill="none" stroke="#339af0" stroke-width="2"/>
    <circle data-layer="DRILL35MM" cx="50" cy="500" r="17.5" 
            fill="none" stroke="#339af0" stroke-width="2"/>
    
    <!-- Handle hole -->
    <circle data-layer="DRILL8MM" cx="350" cy="300" r="4" 
            fill="none" stroke="#339af0" stroke-width="1"/>
  </g>
</svg>`
};

// Methods
const loadExample = (): void => {
  if (selectedExample.value && examples[selectedExample.value as keyof typeof examples]) {
    svgContent.value = examples[selectedExample.value as keyof typeof examples];
  }
};

const loadCADCAM = async (): Promise<void> => {
  if (!viewerContainer.value || !svgContent.value.trim()) return;

  isLoading.value = true;

  try {
    // Destroy existing viewer
    if (zdogViewer.value) {
      zdogViewer.value.destroy();
    }

    // Create new viewer
    zdogViewer.value = createViewer(viewerContainer.value, {
      width: viewerContainer.value.clientWidth,
      height: viewerContainer.value.clientHeight,
      backgroundColor: '#f8f9fa',
      dragRotate: true,
      autoResize: true
    });

    // Load CAD/CAM SVG
    await loadCADCAMSVG(zdogViewer.value, svgContent.value, {
      face: 'both',
      panelThickness: 18,
      depth: 5,
      centerOrigin: true
    });

    // Update state
    doorPanel.value = zdogViewer.value.getDoorPanel();
    layers.value = zdogViewer.value.getLayers();
    totalShapes.value = zdogViewer.value.state.shapes.size;

  } catch (error) {
    console.error('Failed to load CAD/CAM SVG:', error);
  } finally {
    isLoading.value = false;
  }
};

// Event handlers
const onFaceChanged = (face: 'top' | 'bottom' | 'both'): void => {
  console.log('Active face changed to:', face);
};

const onLayerToggled = (layerName: string, visible: boolean): void => {
  console.log(`Layer ${layerName} ${visible ? 'shown' : 'hidden'}`);
};

const onSettingsChanged = (settings: any): void => {
  console.log('Settings changed:', settings);
};

const formatOperationType = (type: string): string => {
  const typeMap: Record<string, string> = {
    'groove': 'Groove',
    'pocket': 'Pocket',
    'drill': 'Drill',
    'v-carve': 'V-Carve',
    'chamfer': 'Chamfer',
    'fillet': 'Fillet',
    'panel': 'Panel'
  };
  
  return typeMap[type] || type.charAt(0).toUpperCase() + type.slice(1);
};

// Lifecycle
onMounted(() => {
  // Check for content from toolbar button
  const storedContent = localStorage.getItem('cadcam-viewer-content');
  if (storedContent) {
    svgContent.value = storedContent;
    localStorage.removeItem('cadcam-viewer-content'); // Clear after use
    // Auto-load the content
    setTimeout(loadCADCAM, 100);
  } else {
    // Load first example by default
    selectedExample.value = 'simple-door';
    loadExample();
  }
});

onUnmounted(() => {
  if (zdogViewer.value) {
    zdogViewer.value.destroy();
  }
});
</script>

<style scoped lang="scss">
.cadcam-test-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.test-header {
  padding: 20px;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
  text-align: center;
  
  h1 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: #495057;
  }
  
  p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
  }
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 2fr 400px;
  flex: 1;
  min-height: 0;
  gap: 1px;
  background: #e9ecef;
}

.editor-panel,
.viewer-panel,
.controls-panel {
  background: #ffffff;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  
  h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
  }
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.example-select {
  padding: 4px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 12px;
}

.load-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.editor-container {
  flex: 1;
  padding: 16px;
}

.svg-editor {
  width: 100%;
  height: 100%;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  resize: none;
  
  &:focus {
    outline: none;
    border-color: #007bff;
  }
}

.viewer-status {
  font-size: 12px;
}

.status {
  display: flex;
  align-items: center;
  gap: 4px;
  
  &.loading { color: #007bff; }
  &.loaded { color: #28a745; }
  &.empty { color: #6c757d; }
}

.viewer-container {
  flex: 1;
  position: relative;
  min-height: 0;
}

.empty-state,
.no-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6c757d;
  text-align: center;
  
  h4 {
    margin: 16px 0 8px 0;
    font-size: 16px;
  }
  
  p {
    margin: 0;
    font-size: 14px;
    opacity: 0.8;
  }
}

.info-panel {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  padding: 16px 20px;
  background: #ffffff;
  border-top: 1px solid #e9ecef;
  
  h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
  }
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  
  .label {
    color: #6c757d;
    font-weight: 500;
  }
  
  .value {
    color: #495057;
    font-weight: 600;
  }
}

.layer-summary {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-height: 120px;
  overflow-y: auto;
}

.layer-summary-item {
  display: grid;
  grid-template-columns: 1fr auto auto auto;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  
  &.operation-groove { background: rgba(255, 107, 107, 0.1); }
  &.operation-pocket { background: rgba(81, 207, 102, 0.1); }
  &.operation-drill { background: rgba(51, 154, 240, 0.1); }
  &.operation-v-carve { background: rgba(255, 212, 59, 0.1); }
  &.operation-panel { background: rgba(212, 165, 116, 0.1); }
}

.layer-name {
  font-weight: 600;
  color: #495057;
}

.layer-type,
.layer-face,
.layer-tool {
  font-weight: 500;
  color: #6c757d;
}

.no-info,
.no-layers {
  color: #6c757d;
  font-size: 12px;
  font-style: italic;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
    grid-template-rows: 300px 1fr 300px;
  }
  
  .info-panel {
    grid-template-columns: 1fr;
  }
}
</style>
