<template>
  <div class="cadcam-controls">
    <!-- Face Selection -->
    <div class="control-panel">
      <h3 class="panel-title">Face Operations</h3>
      
      <div class="face-selector">
        <button
          v-for="face in faces"
          :key="face.value"
          @click="setActiveFace(face.value)"
          :class="['face-button', { active: activeFace === face.value }]"
          :title="face.description"
        >
          <component :is="face.icon" :size="16" />
          {{ face.label }}
        </button>
      </div>
    </div>

    <!-- Layer Management -->
    <div class="control-panel">
      <h3 class="panel-title">Layer Management</h3>
      
      <div class="layer-list">
        <div
          v-for="layer in layers"
          :key="layer.name"
          class="layer-item"
          :class="{ 
            'layer-hidden': !layer.visible,
            [`layer-${layer.operationType}`]: true,
            [`face-${layer.face}`]: true
          }"
        >
          <div class="layer-info">
            <label class="layer-checkbox">
              <input
                type="checkbox"
                :checked="layer.visible"
                @change="toggleLayer(layer.name, $event.target.checked)"
              />
              <span class="layer-name">{{ layer.name }}</span>
            </label>
            
            <div class="layer-details">
              <span class="layer-face">{{ layer.face.toUpperCase() }}</span>
              <span class="layer-operation">{{ formatOperationType(layer.operationType) }}</span>
              <span v-if="layer.toolInfo" class="layer-tool">
                {{ layer.toolInfo.type }} Ø{{ layer.toolInfo.diameter }}mm
              </span>
            </div>
          </div>
          
          <div class="layer-actions">
            <button
              @click="isolateLayer(layer.name)"
              class="layer-action-btn"
              title="Isolate Layer"
            >
              <Eye :size="14" />
            </button>
            
            <button
              @click="focusLayer(layer.name)"
              class="layer-action-btn"
              title="Focus on Layer"
            >
              <Target :size="14" />
            </button>
          </div>
        </div>
      </div>
      
      <div class="layer-controls">
        <button @click="showAllLayers" class="control-button">
          <EyeOff :size="16" />
          Show All
        </button>
        
        <button @click="hideAllLayers" class="control-button">
          <Eye :size="16" />
          Hide All
        </button>
      </div>
    </div>

    <!-- Door Panel Info -->
    <div v-if="doorPanel" class="control-panel">
      <h3 class="panel-title">Door Panel</h3>
      
      <div class="panel-info">
        <div class="info-row">
          <span class="info-label">Dimensions:</span>
          <span class="info-value">{{ doorPanel.width }} × {{ doorPanel.height }}mm</span>
        </div>
        
        <div class="info-row">
          <span class="info-label">Thickness:</span>
          <span class="info-value">{{ doorPanel.thickness }}mm</span>
        </div>
        
        <div class="info-row">
          <span class="info-label">Top Operations:</span>
          <span class="info-value">{{ doorPanel.topLayers.length }}</span>
        </div>
        
        <div class="info-row">
          <span class="info-label">Bottom Operations:</span>
          <span class="info-value">{{ doorPanel.bottomLayers.length }}</span>
        </div>
      </div>
    </div>

    <!-- Operation Settings -->
    <div class="control-panel">
      <h3 class="panel-title">Operation Settings</h3>
      
      <div class="control-group">
        <label class="control-label">Starting Z Position</label>
        <div class="input-group">
          <input
            type="number"
            v-model.number="operationSettings.startingZ"
            @change="updateOperationSettings"
            class="number-input"
            step="0.1"
          />
          <span class="input-unit">mm</span>
        </div>
      </div>
      
      <div class="control-group">
        <label class="control-label">Panel Thickness</label>
        <div class="input-group">
          <input
            type="number"
            v-model.number="operationSettings.panelThickness"
            @change="updateOperationSettings"
            class="number-input"
            step="1"
            min="1"
          />
          <span class="input-unit">mm</span>
        </div>
      </div>
      
      <div class="control-group">
        <label class="control-label">Default Depth</label>
        <div class="input-group">
          <input
            type="number"
            v-model.number="operationSettings.defaultDepth"
            @change="updateOperationSettings"
            class="number-input"
            step="0.5"
            min="0.1"
          />
          <span class="input-unit">mm</span>
        </div>
      </div>
    </div>

    <!-- Visualization Options -->
    <div class="control-panel">
      <h3 class="panel-title">Visualization</h3>
      
      <div class="control-group">
        <label class="checkbox-label">
          <input
            type="checkbox"
            v-model="visualizationOptions.showToolPaths"
            @change="updateVisualization"
          />
          Show Tool Paths
        </label>
      </div>
      
      <div class="control-group">
        <label class="checkbox-label">
          <input
            type="checkbox"
            v-model="visualizationOptions.showMaterialRemoval"
            @change="updateVisualization"
          />
          Show Material Removal
        </label>
      </div>
      
      <div class="control-group">
        <label class="checkbox-label">
          <input
            type="checkbox"
            v-model="visualizationOptions.colorByOperation"
            @change="updateVisualization"
          />
          Color by Operation Type
        </label>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { 
  Eye, 
  EyeOff, 
  Target, 
  ArrowUp, 
  ArrowDown, 
  Square 
} from 'lucide-vue-next';
import type { ZdogSVGViewer } from './ZdogViewer';
import type { DoorPanel, CADCAMLayer } from './CADCAMParser';

interface Props {
  viewer: ZdogSVGViewer;
}

interface Emits {
  (e: 'face-changed', face: 'top' | 'bottom' | 'both'): void;
  (e: 'layer-toggled', layerName: string, visible: boolean): void;
  (e: 'settings-changed', settings: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// State
const activeFace = ref<'top' | 'bottom' | 'both'>('both');
const doorPanel = ref<DoorPanel | null>(null);
const layers = ref<CADCAMLayer[]>([]);

const operationSettings = reactive({
  startingZ: 0,
  panelThickness: 18,
  defaultDepth: 5
});

const visualizationOptions = reactive({
  showToolPaths: false,
  showMaterialRemoval: true,
  colorByOperation: true
});

// Face options
const faces = [
  {
    value: 'top' as const,
    label: 'Top',
    icon: ArrowUp,
    description: 'Show top face operations only'
  },
  {
    value: 'bottom' as const,
    label: 'Bottom',
    icon: ArrowDown,
    description: 'Show bottom face operations only'
  },
  {
    value: 'both' as const,
    label: 'Both',
    icon: Square,
    description: 'Show both top and bottom operations'
  }
];

// Methods
const setActiveFace = (face: 'top' | 'bottom' | 'both'): void => {
  activeFace.value = face;
  props.viewer.setActiveFace(face);
  emit('face-changed', face);
};

const toggleLayer = (layerName: string, visible: boolean): void => {
  props.viewer.toggleLayerVisibility(layerName, visible);
  
  // Update local layer state
  const layer = layers.value.find(l => l.name === layerName);
  if (layer) {
    layer.visible = visible;
  }
  
  emit('layer-toggled', layerName, visible);
};

const isolateLayer = (layerName: string): void => {
  // Hide all layers except the selected one
  layers.value.forEach(layer => {
    const visible = layer.name === layerName;
    layer.visible = visible;
    props.viewer.toggleLayerVisibility(layer.name, visible);
  });
};

const focusLayer = (layerName: string): void => {
  // Focus camera on the layer (implementation would depend on viewer capabilities)
  console.log('Focus on layer:', layerName);
};

const showAllLayers = (): void => {
  layers.value.forEach(layer => {
    layer.visible = true;
    props.viewer.toggleLayerVisibility(layer.name, true);
  });
};

const hideAllLayers = (): void => {
  layers.value.forEach(layer => {
    layer.visible = false;
    props.viewer.toggleLayerVisibility(layer.name, false);
  });
};

const updateOperationSettings = (): void => {
  emit('settings-changed', { ...operationSettings });
};

const updateVisualization = (): void => {
  // Update viewer visualization options
  console.log('Visualization options updated:', visualizationOptions);
};

const formatOperationType = (type: string): string => {
  const typeMap: Record<string, string> = {
    'groove': 'Groove',
    'pocket': 'Pocket',
    'drill': 'Drill',
    'v-carve': 'V-Carve',
    'chamfer': 'Chamfer',
    'fillet': 'Fillet',
    'panel': 'Panel'
  };
  
  return typeMap[type] || type.charAt(0).toUpperCase() + type.slice(1);
};

// Watch for viewer changes
watch(() => props.viewer, (newViewer) => {
  if (newViewer) {
    // Update door panel and layers
    doorPanel.value = newViewer.getDoorPanel();
    layers.value = newViewer.getLayers();
  }
}, { immediate: true });
</script>

<style scoped lang="scss">
.cadcam-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-left: 1px solid #e9ecef;
  min-width: 320px;
  max-width: 400px;
  height: 100%;
  overflow-y: auto;
}

.control-panel {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
}

.panel-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #f1f3f4;
}

.face-selector {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.face-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 8px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  background: #ffffff;
  color: #495057;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
  }
  
  &.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
  }
}

.layer-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.layer-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #ffffff;
  transition: all 0.2s ease;
  
  &.layer-hidden {
    opacity: 0.5;
  }
  
  &.layer-groove { border-left: 4px solid #ff6b6b; }
  &.layer-pocket { border-left: 4px solid #51cf66; }
  &.layer-drill { border-left: 4px solid #339af0; }
  &.layer-v-carve { border-left: 4px solid #ffd43b; }
  &.layer-chamfer { border-left: 4px solid #ff8cc8; }
  &.layer-fillet { border-left: 4px solid #74c0fc; }
  &.layer-panel { border-left: 4px solid #d4a574; }
}

.layer-info {
  flex: 1;
}

.layer-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
}

.layer-name {
  font-weight: 600;
}

.layer-details {
  display: flex;
  gap: 8px;
  margin-top: 4px;
  font-size: 11px;
}

.layer-face,
.layer-operation,
.layer-tool {
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 500;
}

.layer-face {
  background: #e9ecef;
  color: #495057;
}

.layer-operation {
  background: #d1ecf1;
  color: #0c5460;
}

.layer-tool {
  background: #f8d7da;
  color: #721c24;
}

.layer-actions {
  display: flex;
  gap: 4px;
}

.layer-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background: #f8f9fa;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #e9ecef;
    color: #495057;
  }
}

.layer-controls {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.control-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: #ffffff;
  color: #495057;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
  }
}

.panel-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
}

.info-label {
  color: #6c757d;
  font-weight: 500;
}

.info-value {
  color: #495057;
  font-weight: 600;
}

.control-group {
  margin-bottom: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.control-label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #6c757d;
  margin-bottom: 6px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #495057;
  cursor: pointer;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.number-input {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 13px;
  
  &:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }
}

.input-unit {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}
</style>
