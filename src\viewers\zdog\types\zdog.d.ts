/**
 * TypeScript definitions for Zdog pseudo-3D engine
 * Based on Zdog v1.1.3
 */

declare module 'zdog' {
  export interface Vector {
    x: number;
    y: number;
    z: number;
  }

  export interface VectorOptions {
    x?: number;
    y?: number;
    z?: number;
  }

  export class Vector {
    x: number;
    y: number;
    z: number;

    constructor(position?: VectorOptions);
    set(position: VectorOptions): Vector;
    copy(): Vector;
    add(vector: VectorOptions): Vector;
    subtract(vector: VectorOptions): Vector;
    multiply(scalar: number): Vector;
    magnitude(): number;
    magnitude2d(): number;
    normalize(): Vector;
    lerp(vector: VectorOptions, alpha: number): Vector;
  }

  export interface AnchorOptions {
    addTo?: Group | Illustration;
    translate?: VectorOptions;
    rotate?: VectorOptions;
    scale?: VectorOptions | number;
  }

  export class Anchor {
    addTo?: Group | Illustration;
    translate: Vector;
    rotate: Vector;
    scale: Vector;

    constructor(options?: AnchorOptions);
    addChild(shape: Anchor): void;
    removeChild(shape: Anchor): void;
    remove(): void;
    copyGraph(options?: AnchorOptions): Anchor;
    normalizeRotate(): void;
  }

  export interface GroupOptions extends AnchorOptions {
    updateSort?: boolean;
  }

  export class Group extends Anchor {
    updateSort: boolean;
    children: Anchor[];

    constructor(options?: GroupOptions);
    updateSort(): void;
  }

  export interface ShapeOptions extends AnchorOptions {
    stroke?: number | string;
    fill?: boolean | string;
    color?: string;
    visible?: boolean;
    backface?: boolean | string;
  }

  export class Shape extends Anchor {
    stroke: number | string;
    fill: boolean | string;
    color: string;
    visible: boolean;
    backface: boolean | string;

    constructor(options?: ShapeOptions);
    render(renderer: any, renderOptions: any): void;
  }

  export interface EllipseOptions extends ShapeOptions {
    diameter?: number;
    width?: number;
    height?: number;
    quarters?: number;
  }

  export class Ellipse extends Shape {
    diameter: number;
    width: number;
    height: number;
    quarters: number;

    constructor(options?: EllipseOptions);
  }

  export interface RectOptions extends ShapeOptions {
    width?: number;
    height?: number;
  }

  export class Rect extends Shape {
    width: number;
    height: number;

    constructor(options?: RectOptions);
  }

  export interface RoundedRectOptions extends RectOptions {
    cornerRadius?: number;
  }

  export class RoundedRect extends Rect {
    cornerRadius: number;

    constructor(options?: RoundedRectOptions);
  }

  export interface PolygonOptions extends ShapeOptions {
    radius?: number;
    sides?: number;
  }

  export class Polygon extends Shape {
    radius: number;
    sides: number;

    constructor(options?: PolygonOptions);
  }

  export interface PathOptions extends ShapeOptions {
    path?: Array<VectorOptions | string>;
    closed?: boolean;
  }

  export class Shape extends Shape {
    path: Array<Vector | string>;
    closed: boolean;

    constructor(options?: PathOptions);
  }

  export interface BoxOptions extends ShapeOptions {
    width?: number;
    height?: number;
    depth?: number;
    frontFace?: boolean | string;
    rearFace?: boolean | string;
    leftFace?: boolean | string;
    rightFace?: boolean | string;
    topFace?: boolean | string;
    bottomFace?: boolean | string;
  }

  export class Box extends Shape {
    width: number;
    height: number;
    depth: number;
    frontFace: boolean | string;
    rearFace: boolean | string;
    leftFace: boolean | string;
    rightFace: boolean | string;
    topFace: boolean | string;
    bottomFace: boolean | string;

    constructor(options?: BoxOptions);
  }

  export interface CylinderOptions extends ShapeOptions {
    diameter?: number;
    length?: number;
    frontFace?: boolean | string;
    rearFace?: boolean | string;
  }

  export class Cylinder extends Shape {
    diameter: number;
    length: number;
    frontFace: boolean | string;
    rearFace: boolean | string;

    constructor(options?: CylinderOptions);
  }

  export interface ConeOptions extends ShapeOptions {
    diameter?: number;
    length?: number;
    frontFace?: boolean | string;
  }

  export class Cone extends Shape {
    diameter: number;
    length: number;
    frontFace: boolean | string;

    constructor(options?: ConeOptions);
  }

  export interface HemisphereOptions extends ShapeOptions {
    diameter?: number;
    frontFace?: boolean | string;
  }

  export class Hemisphere extends Shape {
    diameter: number;
    frontFace: boolean | string;

    constructor(options?: HemisphereOptions);
  }

  export interface IllustrationOptions extends GroupOptions {
    element?: string | HTMLCanvasElement | HTMLSVGElement;
    width?: number;
    height?: number;
    zoom?: number;
    dragRotate?: boolean;
    resize?: boolean;
    onDragStart?: (pointer: any) => void;
    onDragMove?: (pointer: any) => void;
    onDragEnd?: (pointer: any) => void;
    onResize?: (width: number, height: number) => void;
  }

  export class Illustration extends Group {
    element: HTMLCanvasElement | HTMLSVGElement;
    width: number;
    height: number;
    zoom: number;
    dragRotate: boolean;
    resize: boolean;
    onDragStart?: (pointer: any) => void;
    onDragMove?: (pointer: any) => void;
    onDragEnd?: (pointer: any) => void;
    onResize?: (width: number, height: number) => void;

    constructor(options?: IllustrationOptions);
    updateRenderGraph(): void;
    renderGraph(): void;
    setSize(width: number, height: number): void;
  }

  export const TAU: number;
}
