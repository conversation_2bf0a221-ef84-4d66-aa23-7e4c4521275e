<template>
  <div class="zdog-test-view">
    <div class="test-header">
      <h1>Zdog 3D SVG Viewer Test</h1>
      <p>Test the Zdog SVG viewer with various example SVG files</p>
    </div>

    <div class="test-controls">
      <div class="example-selector">
        <label for="example-select">Choose Example:</label>
        <select 
          id="example-select" 
          v-model="selectedExample" 
          @change="loadSelectedExample"
          class="example-select"
        >
          <option value="">Select an example...</option>
          <option 
            v-for="example in examples" 
            :key="example.name" 
            :value="example.name"
          >
            {{ formatExampleName(example.name) }}
          </option>
        </select>
      </div>

      <div class="test-actions">
        <button @click="loadRandomExample" class="test-button primary">
          <Shuffle :size="16" />
          Random Example
        </button>
        
        <button @click="clearViewer" class="test-button secondary">
          <Trash2 :size="16" />
          Clear
        </button>
        
        <button @click="exportCurrentView" class="test-button success">
          <Download :size="16" />
          Export PNG
        </button>
      </div>
    </div>

    <!-- Editor Viewer Component -->
    <div class="viewer-container">
      <EditorViewer
        ref="editorViewerRef"
        :initial-content="currentSVG"
        language="svg"
        :auto-load="true"
        :split-ratio="0.4"
        @content-changed="onContentChanged"
        @svg-loaded="onSVGLoaded"
        @export-complete="onExportComplete"
        @error="onError"
      />
    </div>

    <!-- Status and Info -->
    <div class="test-status">
      <div class="status-item">
        <span class="status-label">Status:</span>
        <span :class="['status-value', statusClass]">{{ status }}</span>
      </div>
      
      <div class="status-item" v-if="shapeCount > 0">
        <span class="status-label">Shapes:</span>
        <span class="status-value">{{ shapeCount }}</span>
      </div>
      
      <div class="status-item" v-if="loadTime > 0">
        <span class="status-label">Load Time:</span>
        <span class="status-value">{{ loadTime.toFixed(2) }}ms</span>
      </div>
      
      <div class="status-item" v-if="error">
        <span class="status-label">Error:</span>
        <span class="status-value error">{{ error }}</span>
      </div>
    </div>

    <!-- Example Info Panel -->
    <div v-if="selectedExample" class="example-info">
      <h3>{{ formatExampleName(selectedExample) }}</h3>
      <p>{{ getExampleDescription(selectedExample) }}</p>
      
      <details class="svg-source">
        <summary>View SVG Source</summary>
        <pre><code>{{ currentSVG }}</code></pre>
      </details>
    </div>

    <!-- Performance Metrics -->
    <div class="performance-metrics">
      <h3>Performance Metrics</h3>
      <div class="metrics-grid">
        <div class="metric">
          <span class="metric-label">FPS:</span>
          <span class="metric-value">{{ fps }}</span>
        </div>
        <div class="metric">
          <span class="metric-label">Render Time:</span>
          <span class="metric-value">{{ renderTime.toFixed(2) }}ms</span>
        </div>
        <div class="metric">
          <span class="metric-label">Memory:</span>
          <span class="metric-value">{{ memoryUsage }}MB</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { Shuffle, Trash2, Download } from 'lucide-vue-next';
import EditorViewer from '@/integration/EditorViewer.vue';
import { testSVGs, getAllTestSVGs, getRandomTestSVG } from '@/viewers/zdog/examples/test-svgs';

// Refs
const editorViewerRef = ref<InstanceType<typeof EditorViewer>>();

// State
const selectedExample = ref<string>('');
const currentSVG = ref<string>('');
const status = ref<string>('Ready');
const shapeCount = ref<number>(0);
const loadTime = ref<number>(0);
const error = ref<string>('');
const fps = ref<number>(0);
const renderTime = ref<number>(0);
const memoryUsage = ref<number>(0);

// Examples data
const examples = getAllTestSVGs();

// Computed
const statusClass = computed(() => {
  if (error.value) return 'error';
  if (status.value === 'Loading') return 'loading';
  if (status.value === 'Loaded') return 'success';
  return 'ready';
});

// Example descriptions
const exampleDescriptions: Record<string, string> = {
  basicShapes: 'Simple geometric shapes including circles, rectangles, ellipses, and polygons.',
  complexPath: 'Complex SVG paths with quadratic and cubic Bezier curves.',
  groupedElements: 'Elements grouped with various transforms (translate, rotate, scale).',
  logoDesign: 'Logo-like design with gradients and complex shapes.',
  technicalDrawing: 'Technical drawing style with dimension lines and construction elements.',
  artisticDesign: 'Artistic design with gradients, flowing shapes, and decorative elements.',
  geometricPattern: 'Minimal geometric pattern with grid layout and connecting lines.',
  iconSet: 'Collection of common icons demonstrating various SVG techniques.'
};

// Methods
const formatExampleName = (name: string): string => {
  return name.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
};

const getExampleDescription = (name: string): string => {
  return exampleDescriptions[name] || 'Example SVG for testing the Zdog viewer.';
};

const loadSelectedExample = (): void => {
  if (!selectedExample.value) return;
  
  const example = examples.find(ex => ex.name === selectedExample.value);
  if (example) {
    currentSVG.value = example.svg;
    status.value = 'Loading';
    error.value = '';
  }
};

const loadRandomExample = (): void => {
  const randomSVG = getRandomTestSVG();
  const randomExample = examples.find(ex => ex.svg === randomSVG);
  
  if (randomExample) {
    selectedExample.value = randomExample.name;
    currentSVG.value = randomExample.svg;
    status.value = 'Loading';
    error.value = '';
  }
};

const clearViewer = (): void => {
  currentSVG.value = '';
  selectedExample.value = '';
  status.value = 'Ready';
  shapeCount.value = 0;
  loadTime.value = 0;
  error.value = '';
};

const exportCurrentView = async (): Promise<void> => {
  if (!editorViewerRef.value) return;
  
  try {
    // The EditorViewer will handle the export and download
    status.value = 'Exporting';
  } catch (err) {
    error.value = `Export failed: ${err}`;
    status.value = 'Error';
  }
};

// Event handlers
const onContentChanged = (content: string): void => {
  currentSVG.value = content;
};

const onSVGLoaded = (count: number): void => {
  shapeCount.value = count;
  status.value = 'Loaded';
  error.value = '';
};

const onExportComplete = (blob: Blob): void => {
  status.value = 'Export Complete';
  console.log('Export completed:', blob.size, 'bytes');
};

const onError = (errorMessage: string): void => {
  error.value = errorMessage;
  status.value = 'Error';
};

// Performance monitoring
const updatePerformanceMetrics = (): void => {
  // Simulate performance metrics (in a real app, these would come from the viewer)
  fps.value = Math.floor(Math.random() * 60) + 30; // 30-90 FPS
  renderTime.value = Math.random() * 16 + 4; // 4-20ms
  
  // Get memory usage if available
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    memoryUsage.value = Math.round(memory.usedJSHeapSize / 1024 / 1024);
  } else {
    memoryUsage.value = Math.floor(Math.random() * 50) + 10; // 10-60MB
  }
};

// Lifecycle
onMounted(() => {
  // Check for content from toolbar button
  const storedContent = localStorage.getItem('zdog-viewer-content');
  if (storedContent) {
    currentSVG.value = storedContent;
    localStorage.removeItem('zdog-viewer-content'); // Clear after use
  } else if (examples.length > 0) {
    // Load the first example by default
    selectedExample.value = examples[0].name;
    loadSelectedExample();
  }

  // Start performance monitoring
  const performanceInterval = setInterval(updatePerformanceMetrics, 1000);

  // Cleanup on unmount
  return () => {
    clearInterval(performanceInterval);
  };
});
</script>

<style scoped lang="scss">
.zdog-test-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.test-header {
  padding: 20px;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
  text-align: center;
  
  h1 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: #495057;
  }
  
  p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
  }
}

.test-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
  gap: 20px;
}

.example-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  
  label {
    font-size: 14px;
    font-weight: 500;
    color: #495057;
  }
}

.example-select {
  padding: 6px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: #ffffff;
  font-size: 14px;
  color: #495057;
  min-width: 200px;
  
  &:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }
}

.test-actions {
  display: flex;
  gap: 8px;
}

.test-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: #ffffff;
  color: #495057;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
  }
  
  &.primary {
    background: #007bff;
    border-color: #007bff;
    color: white;
    
    &:hover {
      background: #0056b3;
      border-color: #0056b3;
    }
  }
  
  &.secondary {
    background: #6c757d;
    border-color: #6c757d;
    color: white;
    
    &:hover {
      background: #545b62;
      border-color: #545b62;
    }
  }
  
  &.success {
    background: #28a745;
    border-color: #28a745;
    color: white;
    
    &:hover {
      background: #218838;
      border-color: #218838;
    }
  }
}

.viewer-container {
  flex: 1;
  min-height: 0;
}

.test-status {
  display: flex;
  gap: 20px;
  padding: 12px 20px;
  background: #ffffff;
  border-top: 1px solid #e9ecef;
  font-size: 14px;
}

.status-item {
  display: flex;
  gap: 8px;
}

.status-label {
  font-weight: 500;
  color: #6c757d;
}

.status-value {
  color: #495057;
  
  &.ready {
    color: #6c757d;
  }
  
  &.loading {
    color: #007bff;
  }
  
  &.success {
    color: #28a745;
  }
  
  &.error {
    color: #dc3545;
  }
}

.example-info {
  padding: 16px 20px;
  background: #ffffff;
  border-top: 1px solid #e9ecef;
  
  h3 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
  }
  
  p {
    margin: 0 0 16px 0;
    color: #6c757d;
    font-size: 14px;
  }
}

.svg-source {
  summary {
    cursor: pointer;
    font-weight: 500;
    color: #007bff;
    margin-bottom: 8px;
    
    &:hover {
      color: #0056b3;
    }
  }
  
  pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    overflow-x: auto;
    font-size: 12px;
    margin: 8px 0 0 0;
    
    code {
      color: #495057;
    }
  }
}

.performance-metrics {
  padding: 16px 20px;
  background: #ffffff;
  border-top: 1px solid #e9ecef;
  
  h3 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
  }
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.metric {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.metric-label {
  font-size: 12px;
  font-weight: 500;
  color: #6c757d;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

@media (max-width: 768px) {
  .test-controls {
    flex-direction: column;
    gap: 12px;
  }
  
  .test-actions {
    width: 100%;
    justify-content: center;
  }
  
  .test-status {
    flex-direction: column;
    gap: 8px;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
}
</style>
