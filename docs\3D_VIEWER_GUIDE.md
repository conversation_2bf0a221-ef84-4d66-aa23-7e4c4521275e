# 3D SVG Viewer Integration Guide

The Lua Editor now includes powerful 3D SVG visualization capabilities with two specialized viewers for different use cases.

## Quick Access

### Toolbar Button
- Click the **3D cube icon** (📦) in the toolbar to open the appropriate viewer
- The system automatically detects if your content is CAD/CAM related

### Keyboard Shortcut
- Press **Ctrl+Shift+3** to open the 3D viewer with current editor content

### Sidebar Navigation
- Click the **Box icon** in the left sidebar for the general Zdog 3D Viewer
- Click the **Layers icon** in the left sidebar for the CAD/CAM Manufacturing Viewer

## Available Viewers

### 1. Zdog 3D Viewer (General Purpose)
**Best for:** General SVG files, logos, graphics, artistic designs

**Features:**
- Interactive 3D rotation (drag to rotate)
- Configurable depth/extrusion
- Real-time preview as you edit
- Export to PNG, WebP, SVG
- Performance optimization for large files
- Multiple example SVG files included

**How to use:**
1. Write or paste SVG content in the editor
2. Click the 3D cube toolbar button or press Ctrl+Shift+3
3. Your SVG will be converted to an interactive 3D model
4. Use the controls to adjust depth, rotation, and appearance

### 2. CAD/CAM Manufacturing Viewer (Door Manufacturing)
**Best for:** Door panel manufacturing, layer-based operations, tool paths

**Features:**
- **Top/Bottom Face Operations** - Separate visualization for each face
- **Layer Management** - Show/hide individual manufacturing layers
- **Operation Types:**
  - 🔴 Groove operations (K layers)
  - 🟢 Pocket operations
  - 🔵 Drill operations (holes)
  - 🟡 V-carve operations (decorative)
  - 🟣 Chamfer operations (edge finishing)
  - 🟦 Fillet operations (rounded edges)
  - 🟤 Panel material (wood representation)
- **Tool Detection** - Automatically detects tool sizes from layer names (20MM, 30MM, etc.)
- **Manufacturing Parameters** - Depth, feed rates, spindle speeds
- **Face Isolation** - View only top, bottom, or both faces

**Layer Naming Conventions:**
- `PANEL` - Main door panel
- `20MM`, `30MM` - Tool diameter in millimeters
- `K25MM` - Kanal (groove) with 25mm tool
- `H...` - Contour operations
- `V90...` - V-bit operations (90-degree angle)
- `DRILL8MM` - Drill operations with 8mm bit
- `POCKET15MM` - Pocket operations with 15mm tool

**How to use:**
1. Create SVG with layer information using `data-layer` attributes
2. Specify face with `data-face="top"` or `data-face="bottom"`
3. Open the CAD/CAM viewer
4. Use face controls to switch between top/bottom operations
5. Toggle layer visibility to focus on specific operations

## Example SVG for CAD/CAM

```xml
<svg width="600" height="800" viewBox="0 0 600 800">
  <!-- Main panel -->
  <rect data-layer="PANEL" x="0" y="0" width="600" height="800" 
        fill="#d4a574" stroke="#8B4513" stroke-width="2"/>
  
  <!-- Top face groove -->
  <rect data-layer="20MM" data-face="top" x="50" y="50" width="500" height="60" 
        fill="none" stroke="#ff6b6b" stroke-width="2"/>
  
  <!-- Bottom face drill holes -->
  <circle data-layer="DRILL8MM" data-face="bottom" cx="100" cy="150" r="4" 
          fill="none" stroke="#339af0" stroke-width="1"/>
</svg>
```

## Tips and Best Practices

### For General SVG Visualization:
- Use simple, clean SVG paths for best 3D conversion
- Avoid overly complex paths (they will be simplified automatically)
- Use fills and strokes to define shape boundaries
- Test with different depth settings to find the best look

### For CAD/CAM Manufacturing:
- Always include a `PANEL` layer for the main door
- Use consistent layer naming with tool sizes
- Specify face operations clearly with `data-face` attributes
- Keep operation depths reasonable (typically 60% of panel thickness for grooves)
- Use different colors for different operation types for clarity

### Performance Optimization:
- For files with 1000+ shapes, the system automatically uses Web Workers
- Large files are simplified automatically to maintain smooth interaction
- Use the layer visibility controls to focus on specific operations
- Enable viewport culling for better performance with complex models

## Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| **Ctrl+Shift+3** | Open 3D viewer with current content |
| **F2** | Open function browser |
| **Ctrl+S** | Save current file |
| **F5** | Run script |
| **Ctrl+B** | Toggle sidebar |

## Troubleshooting

### SVG Not Loading
- Check that your SVG has valid XML syntax
- Ensure all tags are properly closed
- Verify viewBox and dimensions are set

### CAD/CAM Layers Not Detected
- Make sure you're using `data-layer` attributes
- Check layer naming follows the conventions
- Verify face attributes are set correctly

### Performance Issues
- Reduce the number of shapes in your SVG
- Simplify complex paths
- Use layer visibility to hide unnecessary operations
- Enable performance optimizations in the viewer settings

### 3D Model Looks Wrong
- Check SVG coordinate system and viewBox
- Adjust depth/extrusion settings
- Verify fill and stroke properties
- Use the "Center Origin" option for better positioning

## Integration with Existing Workflow

The 3D viewers integrate seamlessly with your existing Lua editor workflow:

1. **Design in SVG** - Create your door designs using standard SVG
2. **Add Layer Information** - Use data attributes for CAD/CAM operations
3. **Visualize in 3D** - Use the viewers to see your design in 3D
4. **Export Results** - Export 3D visualizations as images
5. **Continue Development** - Return to Lua editing with enhanced understanding

The viewers are designed to complement, not replace, your existing development process.
