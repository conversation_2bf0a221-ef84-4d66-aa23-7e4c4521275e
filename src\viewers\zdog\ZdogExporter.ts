/**
 * Zdog Exporter - Handle export functionality for the 3D SVG viewer
 */

import type { ExportOptions, ZdogViewerError } from './types/viewer-types';
import type { ZdogSVGViewer } from './ZdogViewer';

export interface ExportMetadata {
  timestamp: string;
  format: string;
  dimensions: { width: number; height: number };
  scale: number;
  quality?: number;
  shapeCount: number;
  viewerVersion: string;
}

export interface ExportResult {
  blob: Blob;
  metadata: ExportMetadata;
  size: number;
  dataUrl?: string;
}

export class ZdogExporter {
  private viewer: ZdogSVGViewer;
  private canvas: HTMLCanvasElement;
  private context: CanvasRenderingContext2D;

  constructor(viewer: ZdogSVGViewer) {
    this.viewer = viewer;
    
    // Get the canvas from the viewer
    const element = this.viewer.illustration?.element;
    if (!element || !(element instanceof HTMLCanvasElement)) {
      throw new Error('Viewer must have a canvas element for export functionality');
    }
    
    this.canvas = element;
    const ctx = this.canvas.getContext('2d');
    if (!ctx) {
      throw new Error('Failed to get 2D context from canvas');
    }
    this.context = ctx;
  }

  /**
   * Export the current view as an image
   */
  public async exportImage(options: ExportOptions): Promise<ExportResult> {
    try {
      const exportCanvas = this.createExportCanvas(options);
      const blob = await this.canvasToBlob(exportCanvas, options);
      const metadata = this.createMetadata(options, exportCanvas);
      
      return {
        blob,
        metadata,
        size: blob.size,
        dataUrl: options.format === 'svg' ? undefined : await this.blobToDataUrl(blob)
      };
    } catch (error) {
      throw new Error(`Export failed: ${error}`);
    }
  }

  /**
   * Export as PNG with specific options
   */
  public async exportPNG(options: Partial<ExportOptions> = {}): Promise<ExportResult> {
    return this.exportImage({
      format: 'png',
      quality: 0.9,
      ...options
    });
  }

  /**
   * Export as WebP with specific options
   */
  public async exportWebP(options: Partial<ExportOptions> = {}): Promise<ExportResult> {
    return this.exportImage({
      format: 'webp',
      quality: 0.8,
      ...options
    });
  }

  /**
   * Export as SVG (vector format)
   */
  public async exportSVG(options: Partial<ExportOptions> = {}): Promise<ExportResult> {
    try {
      const svgContent = this.generateSVG(options);
      const blob = new Blob([svgContent], { type: 'image/svg+xml' });
      const metadata = this.createMetadata({ format: 'svg', ...options });
      
      return {
        blob,
        metadata,
        size: blob.size
      };
    } catch (error) {
      throw new Error(`SVG export failed: ${error}`);
    }
  }

  /**
   * Export multiple formats at once
   */
  public async exportMultiple(formats: ExportOptions[]): Promise<Map<string, ExportResult>> {
    const results = new Map<string, ExportResult>();
    
    for (const options of formats) {
      try {
        const result = await this.exportImage(options);
        results.set(options.format, result);
      } catch (error) {
        console.error(`Failed to export ${options.format}:`, error);
      }
    }
    
    return results;
  }

  /**
   * Create a high-resolution export canvas
   */
  private createExportCanvas(options: ExportOptions): HTMLCanvasElement {
    const exportCanvas = document.createElement('canvas');
    const exportCtx = exportCanvas.getContext('2d');
    
    if (!exportCtx) {
      throw new Error('Failed to create export canvas context');
    }

    // Calculate dimensions
    const scale = options.scale || 1;
    const width = options.width || this.canvas.width;
    const height = options.height || this.canvas.height;
    
    exportCanvas.width = width * scale;
    exportCanvas.height = height * scale;

    // Set high-quality rendering
    exportCtx.imageSmoothingEnabled = true;
    exportCtx.imageSmoothingQuality = 'high';

    // Apply background color if specified
    if (options.backgroundColor) {
      exportCtx.fillStyle = options.backgroundColor;
      exportCtx.fillRect(0, 0, exportCanvas.width, exportCanvas.height);
    }

    // Scale the context for high-resolution rendering
    exportCtx.scale(scale, scale);

    // Draw the current canvas content
    exportCtx.drawImage(this.canvas, 0, 0, width, height);

    return exportCanvas;
  }

  /**
   * Convert canvas to blob with specified format and quality
   */
  private async canvasToBlob(canvas: HTMLCanvasElement, options: ExportOptions): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const mimeType = this.getMimeType(options.format);
      const quality = options.quality || 0.9;
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to create blob from canvas'));
          }
        },
        mimeType,
        quality
      );
    });
  }

  /**
   * Generate SVG content from the current view
   */
  private generateSVG(options: Partial<ExportOptions>): string {
    const width = options.width || this.canvas.width;
    const height = options.height || this.canvas.height;
    const backgroundColor = options.backgroundColor || 'transparent';
    
    let svg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" 
     xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <style>
      .zdog-shape { vector-effect: non-scaling-stroke; }
    </style>
  </defs>`;

    // Add background if specified
    if (backgroundColor !== 'transparent') {
      svg += `\n  <rect width="100%" height="100%" fill="${backgroundColor}"/>`;
    }

    // Convert Zdog shapes to SVG elements
    svg += this.convertShapesToSVG();

    svg += '\n</svg>';
    
    return svg;
  }

  /**
   * Convert Zdog shapes to SVG elements
   */
  private convertShapesToSVG(): string {
    let svgContent = '\n  <g class="zdog-shapes">';
    
    // Get all shapes from the viewer
    const shapes = this.viewer.state.shapes;
    
    for (const [id, zdogShape] of shapes) {
      const shape = zdogShape.shape;
      const element = zdogShape.originalElement;
      
      // Convert based on original SVG element type
      switch (element.type) {
        case 'circle':
          svgContent += this.convertCircleToSVG(id, shape, element);
          break;
        case 'rect':
          svgContent += this.convertRectToSVG(id, shape, element);
          break;
        case 'path':
          svgContent += this.convertPathToSVG(id, shape, element);
          break;
        default:
          // Generic shape conversion
          svgContent += this.convertGenericShapeToSVG(id, shape, element);
      }
    }
    
    svgContent += '\n  </g>';
    return svgContent;
  }

  /**
   * Convert circle shape to SVG
   */
  private convertCircleToSVG(id: string, shape: any, element: any): string {
    const bounds = element.bounds || { x: 0, y: 0, width: 20, height: 20 };
    const cx = bounds.x + bounds.width / 2;
    const cy = bounds.y + bounds.height / 2;
    const r = bounds.width / 2;
    
    return `\n    <circle id="${id}" cx="${cx}" cy="${cy}" r="${r}" 
                   fill="${shape.color || '#000000'}" 
                   stroke="${shape.stroke || 'none'}" 
                   class="zdog-shape"/>`;
  }

  /**
   * Convert rectangle shape to SVG
   */
  private convertRectToSVG(id: string, shape: any, element: any): string {
    const bounds = element.bounds || { x: 0, y: 0, width: 20, height: 20 };
    
    return `\n    <rect id="${id}" x="${bounds.x}" y="${bounds.y}" 
                  width="${bounds.width}" height="${bounds.height}"
                  fill="${shape.color || '#000000'}" 
                  stroke="${shape.stroke || 'none'}" 
                  class="zdog-shape"/>`;
  }

  /**
   * Convert path shape to SVG
   */
  private convertPathToSVG(id: string, shape: any, element: any): string {
    const pathData = element.pathData || 'M0,0 L10,10';
    
    return `\n    <path id="${id}" d="${pathData}"
                  fill="${shape.color || '#000000'}" 
                  stroke="${shape.stroke || 'none'}" 
                  class="zdog-shape"/>`;
  }

  /**
   * Convert generic shape to SVG
   */
  private convertGenericShapeToSVG(id: string, shape: any, element: any): string {
    // Fallback to a simple rectangle
    return this.convertRectToSVG(id, shape, element);
  }

  /**
   * Get MIME type for export format
   */
  private getMimeType(format: string): string {
    switch (format) {
      case 'png':
        return 'image/png';
      case 'webp':
        return 'image/webp';
      case 'svg':
        return 'image/svg+xml';
      default:
        return 'image/png';
    }
  }

  /**
   * Convert blob to data URL
   */
  private async blobToDataUrl(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = () => reject(new Error('Failed to convert blob to data URL'));
      reader.readAsDataURL(blob);
    });
  }

  /**
   * Create export metadata
   */
  private createMetadata(options: ExportOptions, canvas?: HTMLCanvasElement): ExportMetadata {
    const dimensions = canvas ? 
      { width: canvas.width, height: canvas.height } :
      { width: options.width || 800, height: options.height || 600 };
    
    return {
      timestamp: new Date().toISOString(),
      format: options.format,
      dimensions,
      scale: options.scale || 1,
      quality: options.quality,
      shapeCount: this.viewer.state.shapes.size,
      viewerVersion: '1.0.0'
    };
  }

  /**
   * Download exported file
   */
  public static downloadBlob(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * Get export file extension for format
   */
  public static getFileExtension(format: string): string {
    switch (format) {
      case 'png':
        return '.png';
      case 'webp':
        return '.webp';
      case 'svg':
        return '.svg';
      default:
        return '.png';
    }
  }

  /**
   * Generate filename with timestamp
   */
  public static generateFilename(format: string, prefix = 'zdog-export'): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
    const extension = ZdogExporter.getFileExtension(format);
    return `${prefix}-${timestamp}${extension}`;
  }
}
