/**
 * Color manipulation utilities for SVG and Zdog integration
 */

export interface RGB {
  r: number;
  g: number;
  b: number;
}

export interface RGBA extends RGB {
  a: number;
}

export interface HSL {
  h: number;
  s: number;
  l: number;
}

export interface HSLA extends HSL {
  a: number;
}

/**
 * Parse CSS color string to RGB values
 */
export function parseColor(color: string): RGB | null {
  // Handle hex colors
  if (color.startsWith('#')) {
    return parseHexColor(color);
  }
  
  // Handle rgb() and rgba() colors
  if (color.startsWith('rgb')) {
    return parseRgbColor(color);
  }
  
  // Handle hsl() and hsla() colors
  if (color.startsWith('hsl')) {
    const hsl = parseHslColor(color);
    return hsl ? hslToRgb(hsl) : null;
  }
  
  // Handle named colors
  return parseNamedColor(color);
}

/**
 * Parse hex color string (#RGB, #RRGGBB, #RRGGBBAA)
 */
export function parseHexColor(hex: string): RGB | null {
  const cleanHex = hex.replace('#', '');
  
  if (cleanHex.length === 3) {
    // #RGB format
    const r = parseInt(cleanHex[0] + cleanHex[0], 16);
    const g = parseInt(cleanHex[1] + cleanHex[1], 16);
    const b = parseInt(cleanHex[2] + cleanHex[2], 16);
    return { r, g, b };
  }
  
  if (cleanHex.length === 6) {
    // #RRGGBB format
    const r = parseInt(cleanHex.substr(0, 2), 16);
    const g = parseInt(cleanHex.substr(2, 2), 16);
    const b = parseInt(cleanHex.substr(4, 2), 16);
    return { r, g, b };
  }
  
  return null;
}

/**
 * Parse rgb() or rgba() color string
 */
export function parseRgbColor(rgb: string): RGB | null {
  const match = rgb.match(/rgba?\(([^)]+)\)/);
  if (!match) return null;
  
  const values = match[1].split(',').map(v => parseFloat(v.trim()));
  if (values.length < 3) return null;
  
  return {
    r: Math.round(values[0]),
    g: Math.round(values[1]),
    b: Math.round(values[2])
  };
}

/**
 * Parse hsl() or hsla() color string
 */
export function parseHslColor(hsl: string): HSL | null {
  const match = hsl.match(/hsla?\(([^)]+)\)/);
  if (!match) return null;
  
  const values = match[1].split(',').map(v => parseFloat(v.trim()));
  if (values.length < 3) return null;
  
  return {
    h: values[0],
    s: values[1],
    l: values[2]
  };
}

/**
 * Parse named color to RGB
 */
export function parseNamedColor(name: string): RGB | null {
  const namedColors: Record<string, RGB> = {
    black: { r: 0, g: 0, b: 0 },
    white: { r: 255, g: 255, b: 255 },
    red: { r: 255, g: 0, b: 0 },
    green: { r: 0, g: 128, b: 0 },
    blue: { r: 0, g: 0, b: 255 },
    yellow: { r: 255, g: 255, b: 0 },
    cyan: { r: 0, g: 255, b: 255 },
    magenta: { r: 255, g: 0, b: 255 },
    orange: { r: 255, g: 165, b: 0 },
    purple: { r: 128, g: 0, b: 128 },
    gray: { r: 128, g: 128, b: 128 },
    grey: { r: 128, g: 128, b: 128 }
  };
  
  return namedColors[name.toLowerCase()] || null;
}

/**
 * Convert RGB to hex string
 */
export function rgbToHex(rgb: RGB): string {
  const toHex = (n: number) => {
    const hex = Math.round(Math.max(0, Math.min(255, n))).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };
  
  return `#${toHex(rgb.r)}${toHex(rgb.g)}${toHex(rgb.b)}`;
}

/**
 * Convert HSL to RGB
 */
export function hslToRgb(hsl: HSL): RGB {
  const h = hsl.h / 360;
  const s = hsl.s / 100;
  const l = hsl.l / 100;
  
  const hue2rgb = (p: number, q: number, t: number): number => {
    if (t < 0) t += 1;
    if (t > 1) t -= 1;
    if (t < 1/6) return p + (q - p) * 6 * t;
    if (t < 1/2) return q;
    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
    return p;
  };
  
  if (s === 0) {
    // Achromatic
    const gray = Math.round(l * 255);
    return { r: gray, g: gray, b: gray };
  }
  
  const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
  const p = 2 * l - q;
  
  return {
    r: Math.round(hue2rgb(p, q, h + 1/3) * 255),
    g: Math.round(hue2rgb(p, q, h) * 255),
    b: Math.round(hue2rgb(p, q, h - 1/3) * 255)
  };
}

/**
 * Convert RGB to HSL
 */
export function rgbToHsl(rgb: RGB): HSL {
  const r = rgb.r / 255;
  const g = rgb.g / 255;
  const b = rgb.b / 255;
  
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const diff = max - min;
  
  const l = (max + min) / 2;
  
  if (diff === 0) {
    return { h: 0, s: 0, l: l * 100 };
  }
  
  const s = l > 0.5 ? diff / (2 - max - min) : diff / (max + min);
  
  let h: number;
  switch (max) {
    case r:
      h = (g - b) / diff + (g < b ? 6 : 0);
      break;
    case g:
      h = (b - r) / diff + 2;
      break;
    case b:
      h = (r - g) / diff + 4;
      break;
    default:
      h = 0;
  }
  h /= 6;
  
  return {
    h: h * 360,
    s: s * 100,
    l: l * 100
  };
}

/**
 * Lighten a color by a percentage
 */
export function lightenColor(color: string, percentage: number): string {
  const rgb = parseColor(color);
  if (!rgb) return color;
  
  const hsl = rgbToHsl(rgb);
  hsl.l = Math.min(100, hsl.l + percentage);
  
  return rgbToHex(hslToRgb(hsl));
}

/**
 * Darken a color by a percentage
 */
export function darkenColor(color: string, percentage: number): string {
  const rgb = parseColor(color);
  if (!rgb) return color;
  
  const hsl = rgbToHsl(rgb);
  hsl.l = Math.max(0, hsl.l - percentage);
  
  return rgbToHex(hslToRgb(hsl));
}

/**
 * Get contrasting color (black or white) for a given color
 */
export function getContrastColor(color: string): string {
  const rgb = parseColor(color);
  if (!rgb) return '#000000';
  
  // Calculate relative luminance
  const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
  
  return luminance > 0.5 ? '#000000' : '#ffffff';
}

/**
 * Blend two colors together
 */
export function blendColors(color1: string, color2: string, ratio: number): string {
  const rgb1 = parseColor(color1);
  const rgb2 = parseColor(color2);
  
  if (!rgb1 || !rgb2) return color1;
  
  const blended: RGB = {
    r: Math.round(rgb1.r + (rgb2.r - rgb1.r) * ratio),
    g: Math.round(rgb1.g + (rgb2.g - rgb1.g) * ratio),
    b: Math.round(rgb1.b + (rgb2.b - rgb1.b) * ratio)
  };
  
  return rgbToHex(blended);
}

/**
 * Generate a random color
 */
export function randomColor(): string {
  const r = Math.floor(Math.random() * 256);
  const g = Math.floor(Math.random() * 256);
  const b = Math.floor(Math.random() * 256);
  
  return rgbToHex({ r, g, b });
}

/**
 * Validate if a string is a valid color
 */
export function isValidColor(color: string): boolean {
  return parseColor(color) !== null;
}
