# Zdog 3D SVG Viewer

A comprehensive TypeScript implementation of a 3D SVG viewer using the Zdog pseudo-3D engine. This viewer converts SVG files into interactive 3D representations with full type safety and performance optimizations.

## Features

### Core Functionality
- **SVG Import & Parsing**: Parse SVG path data (M, L, C, Q, A, Z commands) with full TypeScript types
- **3D Rendering**: Render SVG paths as 3D shapes with configurable depth/extrusion
- **Interactive Controls**: Drag-to-rotate, zoom, pan, and auto-rotation capabilities
- **Real-time Preview**: Live preview updates when SVG code changes
- **Export Functionality**: Export as PNG, WebP, or SVG with customizable options

### Advanced Features
- **Performance Optimization**: Web Worker support for large SVG files
- **Memory Management**: Object pooling and efficient garbage collection
- **Viewport Culling**: Hide shapes outside the visible area for better performance
- **Level of Detail**: Automatic shape simplification based on size and distance
- **Type Safety**: Comprehensive TypeScript interfaces and error handling

### UI Components
- **Split-pane Editor**: Lua editor on left, 3D viewer on right
- **Control Panel**: Sliders for depth, stroke width, rotation speed
- **Toolbar**: Quick access buttons for common operations
- **Status Indicators**: FPS counter, shape count, performance metrics

## Installation

```bash
npm install zdog@^1.1.3
```

## Quick Start

```typescript
import { ZdogSVGViewer } from './viewers/zdog/ZdogViewer';
import { EditorViewer } from './integration/EditorViewer.vue';

// Basic usage with container element
const viewer = new ZdogSVGViewer({
  container: document.getElementById('viewer-container'),
  width: 800,
  height: 600,
  backgroundColor: '#f0f0f0',
  dragRotate: true
});

// Load SVG content
await viewer.loadSVG(`
  <svg viewBox="0 0 100 100">
    <circle cx="50" cy="50" r="25" fill="#ff0000" />
    <rect x="25" y="25" width="50" height="50" fill="#0000ff" />
  </svg>
`, {
  depth: 20,
  strokeWidth: 2,
  centerOrigin: true
});
```

## API Reference

### ZdogSVGViewer

#### Constructor Options
```typescript
interface ZdogViewerOptions {
  container: HTMLElement;
  width?: number;
  height?: number;
  backgroundColor?: string;
  pixelRatio?: number;
  dragRotate?: boolean;
  autoResize?: boolean;
  zoom?: number;
}
```

#### Methods

##### `loadSVG(svgContent: string, options?: SVGImportOptions): Promise<void>`
Load and render SVG content as 3D shapes.

```typescript
const options: SVGImportOptions = {
  depth: 20,              // Extrusion depth
  strokeWidth: 2,         // Default stroke width
  preserveAspectRatio: true,
  scale: 1,
  centerOrigin: true,
  extrudeMode: 'uniform'  // 'uniform' | 'tapered' | 'none'
};

await viewer.loadSVG(svgContent, options);
```

##### `updateShape(id: string, properties: Partial<ShapeProperties>): void`
Update properties of a specific shape.

```typescript
viewer.updateShape('shape-id', {
  color: '#00ff00',
  visible: false,
  translate: { x: 10, y: 0, z: 5 },
  rotate: { x: 0, y: Math.PI / 4, z: 0 }
});
```

##### `exportImage(options: ExportOptions): Promise<Blob>`
Export the current view as an image.

```typescript
const blob = await viewer.exportImage({
  format: 'png',
  quality: 0.9,
  width: 1920,
  height: 1080,
  scale: 2
});
```

#### Events

```typescript
// Listen for events
viewer.on('load', (data) => {
  console.log(`Loaded ${data.shapeCount} shapes in ${data.loadTime}ms`);
});

viewer.on('render', (data) => {
  console.log(`Rendering at ${data.fps} FPS`);
});

viewer.on('error', (data) => {
  console.error('Viewer error:', data.error);
});
```

### SVGParser

#### Parsing SVG Content
```typescript
import { SVGParser } from './viewers/zdog/SVGParser';

const parser = new SVGParser({
  preserveAspectRatio: true,
  ignoreInvisible: true,
  simplifyPaths: true,
  tolerance: 0.1
});

const result = parser.parseSVG(svgContent);
console.log('Parsed elements:', result.elements);
console.log('Errors:', result.errors);
```

#### Path Data Parsing
```typescript
const commands = parser.parsePathData('M10,10 L20,20 C30,30 40,40 50,50 Z');
// Returns array of SVGPathCommand objects
```

### Performance Optimization

#### Web Worker Usage
```typescript
// Large SVG files are automatically processed in a Web Worker
const result = await viewer.loadSVG(largeSVGContent, {
  enableWorker: true,  // Enable Web Worker processing
  maxShapes: 5000,     // Limit number of shapes
  simplifyPaths: true  // Simplify complex paths
});
```

#### Performance Configuration
```typescript
const viewer = new ZdogSVGViewer({
  container,
  // ... other options
});

// Configure performance settings
viewer.configuration.performance = {
  maxShapes: 10000,
  targetFPS: 60,
  enableVirtualization: true,
  memoryLimit: 100 * 1024 * 1024 // 100MB
};
```

## Vue Component Usage

### EditorViewer Component
```vue
<template>
  <EditorViewer
    :initial-content="svgContent"
    language="svg"
    :auto-load="true"
    :split-ratio="0.4"
    @svg-loaded="onSVGLoaded"
    @export-complete="onExportComplete"
    @error="onError"
  />
</template>

<script setup lang="ts">
import EditorViewer from '@/integration/EditorViewer.vue';

const svgContent = ref(`
  <svg viewBox="0 0 200 200">
    <circle cx="100" cy="100" r="50" fill="red" />
  </svg>
`);

const onSVGLoaded = (shapeCount: number) => {
  console.log(`Loaded ${shapeCount} shapes`);
};
</script>
```

### ZdogControls Component
```vue
<template>
  <ZdogControls
    :viewer="zdogViewer"
    @export="onExport"
    @controls-change="onControlsChange"
  />
</template>
```

## Styling

### CSS Classes
```scss
// Import the main stylesheet
@import '@/styles/zdog-viewer.scss';

// Custom styling
.my-viewer {
  .zdog-canvas {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  .zdog-controls {
    background: #f8f9fa;
  }
}
```

### CSS Custom Properties
```css
:root {
  --zdog-primary: #007bff;
  --zdog-background: #ffffff;
  --zdog-border-radius: 4px;
  --zdog-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}
```

## Testing

### Unit Tests
```typescript
import { describe, it, expect } from 'vitest';
import { ZdogSVGViewer } from './ZdogViewer';

describe('ZdogSVGViewer', () => {
  it('should load SVG content', async () => {
    const container = document.createElement('div');
    const viewer = new ZdogSVGViewer({ container });
    
    await viewer.loadSVG('<svg><circle cx="50" cy="50" r="25"/></svg>');
    
    expect(viewer.state.shapes.size).toBeGreaterThan(0);
  });
});
```

### Example SVGs
```typescript
import { testSVGs } from './examples/test-svgs';

// Load a test SVG
await viewer.loadSVG(testSVGs.basicShapes);
```

## Performance Tips

1. **Large Files**: Use Web Workers for SVG files with >1000 elements
2. **Memory**: Enable object pooling for frequently created/destroyed objects
3. **Rendering**: Use viewport culling to hide off-screen shapes
4. **Optimization**: Enable Level of Detail for distant/small shapes
5. **Animation**: Limit concurrent animations to improve performance

## Browser Support

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## TypeScript Support

Full TypeScript support with comprehensive type definitions:

```typescript
import type {
  ZdogViewerOptions,
  ViewerControls,
  ExportOptions,
  SVGImportOptions,
  ViewerEvent
} from './types/viewer-types';
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Changelog

### v1.0.0
- Initial release with full TypeScript support
- SVG parsing and 3D rendering
- Interactive controls and export functionality
- Performance optimizations and Web Worker support
